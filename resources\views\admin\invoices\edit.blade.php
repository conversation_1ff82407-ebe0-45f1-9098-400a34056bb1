@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="Edit Invoice"
        description="Update invoice information"
        :back-route="route('admin.invoices.show', $invoice)"
        back-label="Back to Invoice">
    </x-page-header>

    <form method="POST" action="{{ route('admin.invoices.update', $invoice) }}" class="space-y-6">
        @csrf
        @method('PUT')
        
        <!-- Basic Information -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Basic Information</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Student Selection -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Student</label>

                    @if($students->count() > 0)
                        <!-- Multiselect Search Component -->
                        <div x-data="singleSelectSearch()" class="relative"
                             x-init="
                                items = [
                                    @foreach($students as $student)
                                    {
                                        id: {{ $student->id }},
                                        name: '{{ addslashes($student->user->name ?? '') }}',
                                        subtitle: '{{ addslashes(($student->student_id ?? '') . ' • ' . ($student->class ?? '') . ($student->section ? ' - ' . $student->section : '')) }}',
                                        searchText: '{{ addslashes(($student->user->name ?? '') . ' ' . ($student->student_id ?? '') . ' ' . ($student->class ?? '') . ' ' . ($student->section ?? '')) }}'
                                    },
                                    @endforeach
                                ];
                                selected = {{ old('student_id', $invoice->student_id) }};
                                name = 'student_id';
                                placeholder = 'Search and select a student...';
                                init();
                             ">

                            <!-- Search Input -->
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>
                                <input
                                    type="text"
                                    x-model="searchQuery"
                                    @focus="showDropdown = true"
                                    @click.away="showDropdown = false"
                                    class="search-input @error('student_id') border-red-500 @enderror"
                                    :placeholder="selectedItem ? selectedItem.name : placeholder"
                                    autocomplete="off"
                                    required
                                >
                            </div>

                            <!-- Selected Item Display -->
                            <div x-show="selectedItem" class="mt-2">
                                <div class="inline-flex items-center px-3 py-1 rounded-md text-sm bg-blue-100 text-blue-800">
                                    <span x-text="selectedItem ? selectedItem.name : ''"></span>
                                    <button type="button" @click="clearSelection()" class="ml-2 text-blue-600 hover:text-blue-800">
                                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <!-- Dropdown -->
                            <div x-show="showDropdown && filteredItems.length > 0"
                                 x-transition:enter="transition ease-out duration-100"
                                 x-transition:enter-start="transform opacity-0 scale-95"
                                 x-transition:enter-end="transform opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-75"
                                 x-transition:leave-start="transform opacity-100 scale-100"
                                 x-transition:leave-end="transform opacity-0 scale-95"
                                 class="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base border border-gray-200 overflow-auto focus:outline-none sm:text-sm">
                                <template x-for="item in filteredItems" :key="item.id">
                                    <div @click="selectItem(item)"
                                         class="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-blue-50">
                                        <div class="flex items-center">
                                            <span class="font-medium block truncate" x-text="item.name"></span>
                                        </div>
                                        <span class="text-gray-500 text-sm block truncate" x-text="item.subtitle"></span>
                                    </div>
                                </template>
                            </div>

                            <!-- Hidden input for form submission -->
                            <input type="hidden" :name="name" :value="selectedItem ? selectedItem.id : ''" required>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <p class="text-sm text-gray-500">No students available.</p>
                        </div>
                    @endif

                    @error('student_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Invoice Type -->
                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Invoice Type</label>
                    <select name="type" id="type" class="form-input @error('type') border-red-500 @enderror" required>
                        <option value="">Select type</option>
                        @foreach($invoiceTypes as $key => $label)
                            <option value="{{ $key }}" {{ (old('type', $invoice->type) == $key) ? 'selected' : '' }}>{{ $label }}</option>
                        @endforeach
                    </select>
                    @error('type')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Title -->
                <div class="md:col-span-2">
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Invoice Title</label>
                    <input type="text" name="title" id="title" value="{{ old('title', $invoice->title) }}" 
                           class="form-input @error('title') border-red-500 @enderror" 
                           placeholder="e.g., Monthly Tuition Fee - January 2024" required>
                    @error('title')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Description -->
                <div class="md:col-span-2">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea name="description" id="description" rows="3" 
                              class="form-input @error('description') border-red-500 @enderror"
                              placeholder="Additional details about this invoice...">{{ old('description', $invoice->description) }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Amount and Due Date -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Amount & Due Date</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Amount -->
                <div>
                    <label for="amount" class="block text-sm font-medium text-gray-700 mb-2">Amount (RM)</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 sm:text-sm">RM</span>
                        </div>
                        <input type="number" name="amount" id="amount" value="{{ old('amount', $invoice->amount) }}" 
                               step="0.01" min="0" class="form-input pl-12 @error('amount') border-red-500 @enderror" 
                               placeholder="0.00" required>
                    </div>
                    @error('amount')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Discount -->
                <div>
                    <label for="discount" class="block text-sm font-medium text-gray-700 mb-2">Discount (RM)</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 sm:text-sm">RM</span>
                        </div>
                        <input type="number" name="discount" id="discount" value="{{ old('discount', $invoice->discount) }}" 
                               step="0.01" min="0" class="form-input pl-12 @error('discount') border-red-500 @enderror" 
                               placeholder="0.00">
                    </div>
                    @error('discount')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Due Date -->
                <div>
                    <label for="due_date" class="block text-sm font-medium text-gray-700 mb-2">Due Date</label>
                    <input type="date" name="due_date" id="due_date" value="{{ old('due_date', $invoice->due_date->format('Y-m-d')) }}" 
                           class="form-input @error('due_date') border-red-500 @enderror" 
                           min="{{ date('Y-m-d', strtotime('+1 day')) }}" required>
                    @error('due_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Total Amount Display -->
            <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                <div class="flex justify-between items-center">
                    <span class="text-lg font-medium text-gray-900">Total Amount:</span>
                    <span id="total-amount" class="text-xl font-bold text-blue-600">RM {{ number_format($invoice->total_amount, 2) }}</span>
                </div>
            </div>
        </div>

        <!-- Line Items (Optional) -->
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-medium text-gray-900">Line Items (Optional)</h3>
                <button type="button" id="add-line-item" class="btn-secondary text-sm">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add Line Item
                </button>
            </div>
            
            <div id="line-items-container" class="space-y-4">
                @if($invoice->line_items && count($invoice->line_items) > 0)
                    @foreach($invoice->line_items as $index => $item)
                        <div class="line-item border border-gray-200 rounded-lg p-4">
                            <div class="flex justify-between items-start mb-4">
                                <h4 class="text-sm font-medium text-gray-900">Line Item {{ $index + 1 }}</h4>
                                <button type="button" class="remove-line-item text-red-600 hover:text-red-900">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                                    <input type="text" name="line_items[{{ $index }}][description]" 
                                           value="{{ $item['description'] ?? '' }}"
                                           class="form-input" placeholder="Item description">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Amount (RM)</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <span class="text-gray-500 sm:text-sm">RM</span>
                                        </div>
                                        <input type="number" name="line_items[{{ $index }}][amount]" 
                                               value="{{ $item['amount'] ?? '' }}"
                                               step="0.01" min="0" class="form-input pl-12" placeholder="0.00">
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                @endif
            </div>
        </div>

        <!-- Actions -->
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div></div> <!-- Empty div for spacing -->
                <div class="flex items-center space-x-4">
                    <a href="{{ route('admin.invoices.show', $invoice) }}" class="btn-cancel">Cancel</a>
                    <button type="submit" class="btn-primary">Update Invoice</button>
                </div>
            </div>
        </div>
    </form>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const amountInput = document.getElementById('amount');
    const discountInput = document.getElementById('discount');
    const totalAmountDisplay = document.getElementById('total-amount');
    const addLineItemBtn = document.getElementById('add-line-item');
    const lineItemsContainer = document.getElementById('line-items-container');
    let lineItemCount = {{ $invoice->line_items ? count($invoice->line_items) : 0 }};

    // Calculate total amount
    function calculateTotal() {
        const amount = parseFloat(amountInput.value) || 0;
        const discount = parseFloat(discountInput.value) || 0;

        // Calculate line items total
        let lineItemsTotal = 0;
        const lineItemAmounts = document.querySelectorAll('input[name*="[amount]"]');
        lineItemAmounts.forEach(input => {
            lineItemsTotal += parseFloat(input.value) || 0;
        });

        const total = Math.max(0, amount + lineItemsTotal - discount);
        totalAmountDisplay.textContent = `RM ${total.toFixed(2)}`;
    }

    // Add event listeners for amount calculation
    amountInput.addEventListener('input', calculateTotal);
    discountInput.addEventListener('input', calculateTotal);

    // Add line item functionality
    addLineItemBtn.addEventListener('click', function() {
        const lineItemHtml = `
            <div class="line-item border border-gray-200 rounded-lg p-4">
                <div class="flex justify-between items-start mb-4">
                    <h4 class="text-sm font-medium text-gray-900">Line Item ${lineItemCount + 1}</h4>
                    <button type="button" class="remove-line-item text-red-600 hover:text-red-900">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <input type="text" name="line_items[${lineItemCount}][description]" 
                               class="form-input" placeholder="Item description">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Amount (RM)</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 sm:text-sm">RM</span>
                            </div>
                            <input type="number" name="line_items[${lineItemCount}][amount]" 
                                   step="0.01" min="0" class="form-input pl-12" placeholder="0.00">
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        lineItemsContainer.insertAdjacentHTML('beforeend', lineItemHtml);

        // Add event listener to the new amount input for total calculation
        const newAmountInput = lineItemsContainer.querySelector(`input[name="line_items[${lineItemCount}][amount]"]`);
        newAmountInput.addEventListener('input', calculateTotal);

        lineItemCount++;
    });

    // Remove line item functionality
    lineItemsContainer.addEventListener('click', function(e) {
        if (e.target.closest('.remove-line-item')) {
            e.target.closest('.line-item').remove();
            calculateTotal(); // Recalculate total after removing item
        }
    });

    // Add event listeners to existing line item amount inputs
    const existingLineItemAmounts = document.querySelectorAll('input[name*="[amount]"]');
    existingLineItemAmounts.forEach(input => {
        input.addEventListener('input', calculateTotal);
    });

    // Initial calculation
    calculateTotal();
});

// Single Select Search Component
function singleSelectSearch() {
    return {
        items: [],
        selectedItem: null,
        searchQuery: '',
        showDropdown: false,
        name: 'item',
        placeholder: 'Search and select an item...',
        selected: null,

        init() {
            // Initialize selected item based on this.selected
            if (this.selected) {
                this.selectedItem = this.items.find(item => item.id == this.selected);
                if (this.selectedItem) {
                    this.searchQuery = this.selectedItem.name;
                }
            }
        },

        get filteredItems() {
            if (!this.searchQuery) {
                return this.items;
            }

            return this.items.filter(item => {
                return item.searchText.toLowerCase().includes(this.searchQuery.toLowerCase());
            });
        },

        selectItem(item) {
            this.selectedItem = item;
            this.searchQuery = item.name;
            this.showDropdown = false;
        },

        clearSelection() {
            this.selectedItem = null;
            this.searchQuery = '';
            this.showDropdown = false;
        }
    };
}
</script>
@endpush
@endsection
