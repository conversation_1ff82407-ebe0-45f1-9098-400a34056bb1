<aside class="fixed inset-y-0 left-0 z-30 w-64 bg-white shadow-lg border-r border-gray-200">
    <div class="flex flex-col h-full">



        <!-- Navigation Menu -->
        <nav class="flex-1 py-6 space-y-2 px-4">
            <?php if(auth()->user()->user_type === 'admin'): ?>
                <!-- Admin Navigation -->
                <a href="<?php echo e(route('admin.dashboard')); ?>" class="sidebar-link <?php echo e(request()->routeIs('admin.dashboard') ? 'active' : ''); ?> justify-start">
                    <svg class="w-5 h-5 flex-shrink-0 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                    </svg>
                    <span>Dashboard</span>
                </a>

                <a href="<?php echo e(route('admin.invoices.index')); ?>" class="sidebar-link <?php echo e(request()->routeIs('admin.invoices.*') ? 'active' : ''); ?> justify-start">
                    <svg class="w-5 h-5 flex-shrink-0 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <span>Invoices</span>
                </a>

                <a href="<?php echo e(route('admin.payments.index')); ?>" class="sidebar-link <?php echo e(request()->routeIs('admin.payments.*') ? 'active' : ''); ?> justify-start">
                    <svg class="w-5 h-5 flex-shrink-0 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    <span>Payments</span>
                </a>

                <a href="<?php echo e(route('admin.students.index')); ?>" class="sidebar-link <?php echo e(request()->routeIs('admin.students.*') ? 'active' : ''); ?> justify-start">
                    <svg class="w-5 h-5 flex-shrink-0 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                    </svg>
                    <span>Students</span>
                </a>

                <a href="<?php echo e(route('admin.teachers.index')); ?>" class="sidebar-link <?php echo e(request()->routeIs('admin.teachers.*') ? 'active' : ''); ?> justify-start">
                    <svg class="w-5 h-5 flex-shrink-0 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    <span>Teachers</span>
                </a>

                <a href="<?php echo e(route('admin.guardians.index')); ?>" class="sidebar-link <?php echo e(request()->routeIs('admin.guardians.*') ? 'active' : ''); ?> justify-start">
                    <svg class="w-5 h-5 flex-shrink-0 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    <span>Parents/Guardians</span>
                </a>

                <a href="<?php echo e(route('admin.relationships.index')); ?>" class="sidebar-link <?php echo e(request()->routeIs('admin.relationships.*') ? 'active' : ''); ?> justify-start">
                    <svg class="w-5 h-5 flex-shrink-0 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    <span>Relationships</span>
                </a>

                <a href="<?php echo e(route('admin.attendance.index')); ?>" class="sidebar-link <?php echo e(request()->routeIs('admin.attendance.*') ? 'active' : ''); ?> justify-start">
                    <svg class="w-5 h-5 flex-shrink-0 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                    </svg>
                    <span>Attendance</span>
                </a>

                <a href="<?php echo e(route('admin.activity-logs.index')); ?>" class="sidebar-link <?php echo e(request()->routeIs('admin.activity-logs.*') ? 'active' : ''); ?> justify-start">
                    <svg class="w-5 h-5 flex-shrink-0 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <span>Activity Logs</span>
                </a>

                <!-- Academic Management Section -->
                <div class="mt-6 mb-2">
                    <h3 class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider">Academic Management</h3>
                </div>

                <a href="<?php echo e(route('admin.academic.subjects.index')); ?>" class="sidebar-link <?php echo e(request()->routeIs('admin.academic.subjects.*') ? 'active' : ''); ?> justify-start">
                    <svg class="w-5 h-5 flex-shrink-0 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                    <span>Subjects</span>
                </a>

                <a href="<?php echo e(route('admin.academic.classes.index')); ?>" class="sidebar-link <?php echo e(request()->routeIs('admin.academic.classes.*') ? 'active' : ''); ?> justify-start">
                    <svg class="w-5 h-5 flex-shrink-0 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    <span>Classes & Sections</span>
                </a>

                <a href="<?php echo e(route('admin.academic-calendar.index')); ?>" class="sidebar-link <?php echo e(request()->routeIs('admin.academic-calendar.*') || request()->routeIs('admin.schedules.*') || request()->routeIs('admin.academic-years.*') || request()->routeIs('admin.academic-terms.*') || request()->routeIs('admin.time-slots.*') || request()->routeIs('admin.school-events.*') || request()->routeIs('admin.announcements.*') ? 'active' : ''); ?> justify-start">
                    <svg class="w-5 h-5 flex-shrink-0 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <span>Academic Calendar</span>
                </a>

                <!-- Grading & Assessment Section -->
                <div class="mt-6 mb-2">
                    <h3 class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider">Grading & Assessment</h3>
                </div>

                <a href="<?php echo e(route('admin.grading.grade-scales.index')); ?>" class="sidebar-link <?php echo e(request()->routeIs('admin.grading.grade-scales.*') ? 'active' : ''); ?> justify-start">
                    <svg class="w-5 h-5 flex-shrink-0 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <span>Grade Scales</span>
                </a>

                <a href="<?php echo e(route('admin.grading.grade-categories.index')); ?>" class="sidebar-link <?php echo e(request()->routeIs('admin.grading.grade-categories.*') ? 'active' : ''); ?> justify-start">
                    <svg class="w-5 h-5 flex-shrink-0 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                    <span>Grade Categories</span>
                </a>

                <a href="<?php echo e(route('admin.grading.exams.index')); ?>" class="sidebar-link <?php echo e(request()->routeIs('admin.grading.exams.*') ? 'active' : ''); ?> justify-start">
                    <svg class="w-5 h-5 flex-shrink-0 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                    </svg>
                    <span>Exams</span>
                </a>

                <a href="<?php echo e(route('admin.grading.assignments.index')); ?>" class="sidebar-link <?php echo e(request()->routeIs('admin.grading.assignments.*') ? 'active' : ''); ?> justify-start">
                    <svg class="w-5 h-5 flex-shrink-0 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                    </svg>
                    <span>Assignments</span>
                </a>

                <a href="<?php echo e(route('admin.grading.grades.index')); ?>" class="sidebar-link <?php echo e(request()->routeIs('admin.grading.grades.*') ? 'active' : ''); ?> justify-start">
                    <svg class="w-5 h-5 flex-shrink-0 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                    </svg>
                    <span>Grades</span>
                </a>

                <a href="<?php echo e(route('admin.grading.grade-reports.index')); ?>" class="sidebar-link <?php echo e(request()->routeIs('admin.grading.grade-reports.*') ? 'active' : ''); ?> justify-start">
                    <svg class="w-5 h-5 flex-shrink-0 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <span>Grade Reports</span>
                </a>

            <?php elseif(auth()->user()->user_type === 'teacher'): ?>
                <!-- Teacher Navigation -->
                <a href="<?php echo e(route('teacher.dashboard')); ?>" class="sidebar-link <?php echo e(request()->routeIs('teacher.dashboard') ? 'active' : ''); ?> justify-start">
                    <svg class="w-5 h-5 flex-shrink-0 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                    </svg>
                    <span>Dashboard</span>
                </a>

                <a href="<?php echo e(route('teacher.invoices.index')); ?>" class="sidebar-link <?php echo e(request()->routeIs('teacher.invoices.*') ? 'active' : ''); ?> justify-start">
                    <svg class="w-5 h-5 flex-shrink-0 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <span>Invoices</span>
                </a>

                <a href="<?php echo e(route('teacher.students.index')); ?>" class="sidebar-link <?php echo e(request()->routeIs('teacher.students.*') ? 'active' : ''); ?> justify-start">
                    <svg class="w-5 h-5 flex-shrink-0 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                    </svg>
                    <span>My Students</span>
                </a>

            <?php elseif(auth()->user()->user_type === 'guardian'): ?>
                <!-- Guardian/Parent Navigation -->
                <a href="<?php echo e(route('guardian.dashboard')); ?>" class="sidebar-link <?php echo e(request()->routeIs('guardian.dashboard') ? 'active' : ''); ?> justify-start">
                    <svg class="w-5 h-5 flex-shrink-0 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                    </svg>
                    <span>Dashboard</span>
                </a>

                <a href="<?php echo e(route('guardian.invoices.index')); ?>" class="sidebar-link <?php echo e(request()->routeIs('guardian.invoices.*') ? 'active' : ''); ?> justify-start relative">
                    <svg class="w-5 h-5 flex-shrink-0 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <span>Invoices & Bills</span>
                    <?php
                        $guardian = auth()->user()->guardian;
                        $pendingCount = 0;
                        if ($guardian) {
                            $studentIds = $guardian->students->pluck('id');
                            $pendingCount = \App\Models\Invoice::whereIn('student_id', $studentIds)
                                                              ->whereIn('status', ['sent', 'overdue'])
                                                              ->count();
                        }
                    ?>
                    <?php if($pendingCount > 0): ?>
                        <span class="notification-badge"><?php echo e($pendingCount > 99 ? '99+' : $pendingCount); ?></span>
                    <?php endif; ?>
                </a>

                <a href="<?php echo e(route('guardian.payments.index')); ?>" class="sidebar-link <?php echo e(request()->routeIs('guardian.payments.*') ? 'active' : ''); ?> justify-start">
                    <svg class="w-5 h-5 flex-shrink-0 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    <span>Payment History</span>
                </a>

                <a href="<?php echo e(route('guardian.children.index')); ?>" class="sidebar-link <?php echo e(request()->routeIs('guardian.children.*') ? 'active' : ''); ?> justify-start">
                    <svg class="w-5 h-5 flex-shrink-0 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                    <span>My Children</span>
                </a>

            <?php else: ?>
                <!-- Student Navigation -->
                <a href="<?php echo e(route('student.dashboard')); ?>" class="sidebar-link <?php echo e(request()->routeIs('student.dashboard') ? 'active' : ''); ?> justify-start">
                    <svg class="w-5 h-5 flex-shrink-0 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                    </svg>
                    <span>Dashboard</span>
                </a>

                <a href="<?php echo e(route('student.grades.index')); ?>" class="sidebar-link <?php echo e(request()->routeIs('student.grades.*') ? 'active' : ''); ?> justify-start">
                    <svg class="w-5 h-5 flex-shrink-0 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <span>My Grades</span>
                </a>
            <?php endif; ?>
        </nav>

        <!-- User Profile Section -->
        <div class="border-t border-gray-200 bg-gray-50">
            <div class="p-4" x-data="{ userMenuOpen: false }">
                <div class="flex items-center">
                    <button @click="userMenuOpen = !userMenuOpen"
                            class="flex items-center w-full text-left focus:outline-none rounded-lg p-2 hover:bg-gray-100 transition-colors">
                        <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                            <span class="text-white text-sm font-medium">
                                <?php echo e(substr(auth()->user()->name, 0, 2)); ?>

                            </span>
                        </div>
                        <div class="ml-3 flex-1">
                            <p class="text-sm font-medium text-gray-900"><?php echo e(auth()->user()->name); ?></p>
                            <p class="text-xs text-gray-500"><?php echo e(ucfirst(auth()->user()->user_type)); ?></p>
                        </div>
                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  :d="userMenuOpen ? 'M5 15l7-7 7 7' : 'M19 9l-7 7-7-7'"></path>
                        </svg>
                    </button>
                </div>

                <!-- User Menu Dropdown -->
                <div x-show="userMenuOpen"
                     x-transition:enter="transition ease-out duration-100"
                     x-transition:enter-start="transform opacity-0 scale-95"
                     x-transition:enter-end="transform opacity-100 scale-100"
                     x-transition:leave="transition ease-in duration-75"
                     x-transition:leave-start="transform opacity-100 scale-100"
                     x-transition:leave-end="transform opacity-0 scale-95"
                     class="mt-2 space-y-1">
                    <a href="#" class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors">
                        <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        Profile
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors">
                        <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        Settings
                    </a>
                    <div class="border-t border-gray-200 my-1"></div>
                    <form method="POST" action="<?php echo e(route('logout')); ?>">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="flex items-center w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors">
                            <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                            </svg>
                            Logout
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</aside>
<?php /**PATH C:\xampp\htdocs\school-management-system\resources\views/components/sidebar.blade.php ENDPATH**/ ?>