<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <?php if (isset($component)) { $__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.page-header','data' => ['title' => 'Student Management','description' => 'Manage student information and records','backRoute' => route('admin.dashboard'),'backLabel' => 'Back to Dashboard']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('page-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Student Management','description' => 'Manage student information and records','back-route' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.dashboard')),'back-label' => 'Back to Dashboard']); ?>
        <a href="<?php echo e(route('admin.students.create')); ?>" class="btn-primary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Add Student
        </a>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e)): ?>
<?php $attributes = $__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e; ?>
<?php unset($__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e)): ?>
<?php $component = $__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e; ?>
<?php unset($__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e); ?>
<?php endif; ?>

    <!-- Search and Filters -->
    <div class="bg-white shadow rounded-lg p-6"
         x-data="{
             searchQuery: '<?php echo e(request('search')); ?>',
             class: '<?php echo e(request('class')); ?>',
             section: '<?php echo e(request('section')); ?>',
             status: '<?php echo e(request('status')); ?>',
             dateFrom: '<?php echo e(request('date_from')); ?>',
             dateTo: '<?php echo e(request('date_to')); ?>',
             showAdvanced: false
         }">

        <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
            <!-- Search Input -->
            <div class="flex-1">
                <input
                    type="text"
                    x-model="searchQuery"
                    @input="filterStudents()"
                    placeholder="Search students by name, email, phone, address, student ID, class, section, roll number, blood group, medical conditions, emergency contact, gender, or guardian name..."
                    class="form-input w-full"
                >
            </div>

            <!-- Filter Toggle and Clear -->
            <div class="flex items-center space-x-3">
                <button
                    @click="showAdvanced = !showAdvanced"
                    class="inline-flex items-center px-4 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-12"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"></path>
                    </svg>
                    Filters
                    <svg class="ml-2 h-4 w-4 transition-transform duration-200" :class="{ 'rotate-180': showAdvanced }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>

                <button
                    @click="clearStudentFilters()"
                    class="inline-flex items-center px-4 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-12"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Clear All Filters
                </button>
            </div>
        </div>

        <!-- Advanced Filters -->
        <div x-show="showAdvanced" x-transition class="mt-6 pt-6 border-t border-gray-200">
            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <!-- Class Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Class</label>
                    <select x-model="class" @change="filterStudents()" class="form-select">
                        <option value="">All Classes</option>
                        <?php $__currentLoopData = $classes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $classOption): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($classOption); ?>"><?php echo e($classOption); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <!-- Section Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Section</label>
                    <select x-model="section" @change="filterStudents()" class="form-select">
                        <option value="">All Sections</option>
                        <?php $__currentLoopData = $sections; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sectionOption): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($sectionOption); ?>"><?php echo e($sectionOption); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <!-- Status Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select x-model="status" @change="filterStudents()" class="form-select">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                </div>

                <!-- Date From -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Admission From</label>
                    <input type="date" x-model="dateFrom" @change="filterStudents()" class="form-input">
                </div>

                <!-- Date To -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Admission To</label>
                    <input type="date" x-model="dateTo" @change="filterStudents()" class="form-input">
                </div>
            </div>
        </div>

        <!-- Results Count -->
        <div class="mt-4 flex items-center justify-between">
            <span class="text-sm text-gray-500">
                <span data-results-count><?php echo e($stats['total']); ?></span> students found
            </span>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-blue-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Total Students</dt>
                            <dd class="stat-card-value"><?php echo e($stats['total']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-green-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Active</dt>
                            <dd class="stat-card-value"><?php echo e($stats['active']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-red-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Inactive</dt>
                            <dd class="stat-card-value"><?php echo e($stats['inactive']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-purple-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">New This Month</dt>
                            <dd class="stat-card-value"><?php echo e($stats['new_this_month']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Students -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">Current Students</h3>
                <span class="text-sm text-gray-500">
                    <span data-results-count><?php echo e($stats['total']); ?></span> students found
                </span>
            </div>
        </div>
        <div class="p-6">
            <?php if($students->count() > 0): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <?php $__currentLoopData = $students; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $student): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-all duration-200 card-hover student-card relative"
                             data-filterable
                             data-search-text="<?php echo e($student->user->name); ?> <?php echo e($student->user->email); ?> <?php echo e($student->user->phone ?? ''); ?> <?php echo e($student->student_id); ?> <?php echo e($student->class); ?> <?php echo e($student->section); ?> <?php echo e($student->roll_number); ?> <?php echo e($student->gender); ?> <?php echo e($student->blood_group ?? ''); ?> <?php echo e($student->admission_date->format('M d, Y')); ?> <?php echo e($student->user->is_active ? 'active' : 'inactive'); ?>"
                             data-class="<?php echo e($student->class); ?>"
                             data-section="<?php echo e($student->section); ?>"
                             data-date="<?php echo e($student->admission_date->format('Y-m-d')); ?>"
                             data-status="<?php echo e($student->user->is_active ? 'active' : 'inactive'); ?>">

                            <!-- 3-dot menu -->
                            <div class="absolute top-2 right-2 z-30" style="right: 8px; top: 8px;">
                                <button class="three-dot-menu p-1 rounded-full hover:bg-gray-100 transition-colors duration-200" onclick="toggleCardMenu(this)">
                                    <svg class="w-4 h-4 text-gray-500" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                                    </svg>
                                </button>
                                <!-- Dropdown menu (hidden by default) -->
                                <div class="card-menu absolute right-0 mt-1 w-32 bg-white rounded-md shadow-lg border border-gray-200 z-40 hidden">
                                    <div class="py-1">
                                        <a href="<?php echo e(route('admin.students.show', $student)); ?>" class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                            View
                                        </a>
                                        <a href="<?php echo e(route('admin.students.edit', $student)); ?>" class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                            </svg>
                                            Edit
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center shadow-md">
                                        <span class="text-white font-bold text-base">
                                            <?php echo e(substr($student->user->name, 0, 2)); ?>

                                        </span>
                                    </div>
                                </div>
                                <div class="ml-3 flex-1 min-w-0">
                                    <h4 class="text-base font-semibold text-gray-900 truncate"><?php echo e($student->user->name); ?></h4>
                                    <p class="text-sm text-gray-600 mt-1">ID: <?php echo e($student->student_id); ?></p>
                                    <div class="flex items-center mt-1 space-x-2">
                                        <span class="badge badge-blue text-xs">
                                            <?php echo e($student->class); ?> - <?php echo e($student->section); ?>

                                        </span>
                                        <span class="badge badge-gray text-xs">
                                            Roll: <?php echo e($student->roll_number); ?>

                                        </span>
                                        <span class="badge <?php echo e($student->user->is_active ? 'badge-green' : 'badge-red'); ?> text-xs">
                                            <?php echo e($student->user->is_active ? 'Active' : 'Inactive'); ?>

                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-2">
                                <div class="grid grid-cols-2 gap-2">
                                    <div>
                                        <p class="text-xs font-medium text-gray-700">Personal Info</p>
                                        <div class="mt-1">
                                            <div class="flex items-center space-x-1">
                                                <span class="badge <?php echo e($student->gender === 'male' ? 'badge-blue' : 'badge-purple'); ?> text-xs">
                                                    <?php echo e(ucfirst($student->gender)); ?>

                                                </span>
                                                <?php if($student->blood_group): ?>
                                                    <span class="badge badge-red text-xs">
                                                        <?php echo e($student->blood_group); ?>

                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <p class="text-xs font-medium text-gray-700">Admission</p>
                                        <p class="text-xs text-gray-600 mt-1"><?php echo e($student->admission_date->format('M d, Y')); ?></p>
                                    </div>
                                </div>
                            </div>


                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php else: ?>
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No students found</h3>
                    <p class="mt-1 text-sm text-gray-500">Get started by adding your first student.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

</div>

<?php $__env->startPush('scripts'); ?>
<script>
// Toggle card menu function
function toggleCardMenu(button) {
    const menu = button.nextElementSibling;
    const allMenus = document.querySelectorAll('.card-menu');

    // Close all other menus
    allMenus.forEach(m => {
        if (m !== menu) {
            m.classList.add('hidden');
        }
    });

    // Toggle current menu
    menu.classList.toggle('hidden');
}

// Close menus when clicking outside
document.addEventListener('click', function(event) {
    if (!event.target.closest('.three-dot-menu') && !event.target.closest('.card-menu')) {
        const allMenus = document.querySelectorAll('.card-menu');
        allMenus.forEach(menu => {
            menu.classList.add('hidden');
        });
    }
});

// Student filtering functionality
function filterStudents() {
    const searchQuery = document.querySelector('[x-model="searchQuery"]').value.toLowerCase();
    const classFilter = document.querySelector('[x-model="class"]').value;
    const sectionFilter = document.querySelector('[x-model="section"]').value;
    const statusFilter = document.querySelector('[x-model="status"]').value;
    const dateFromFilter = document.querySelector('[x-model="dateFrom"]').value;
    const dateToFilter = document.querySelector('[x-model="dateTo"]').value;

    const students = document.querySelectorAll('[data-filterable]');
    let visibleCount = 0;

    students.forEach(student => {
        const searchText = student.getAttribute('data-search-text').toLowerCase();
        const studentClass = student.getAttribute('data-class');
        const studentSection = student.getAttribute('data-section');
        const studentStatus = student.getAttribute('data-status');
        const studentDate = student.getAttribute('data-date');

        let isVisible = true;

        // Search filter
        if (searchQuery && !searchText.includes(searchQuery)) {
            isVisible = false;
        }

        // Class filter
        if (classFilter && studentClass !== classFilter) {
            isVisible = false;
        }

        // Section filter
        if (sectionFilter && studentSection !== sectionFilter) {
            isVisible = false;
        }

        // Status filter
        if (statusFilter && studentStatus !== statusFilter) {
            isVisible = false;
        }

        // Date range filter
        if (dateFromFilter && studentDate < dateFromFilter) {
            isVisible = false;
        }

        if (dateToFilter && studentDate > dateToFilter) {
            isVisible = false;
        }

        // Show/hide student
        student.style.display = isVisible ? '' : 'none';
        if (isVisible) visibleCount++;
    });

    // Update results count
    const resultsCount = document.querySelector('[data-results-count]');
    if (resultsCount) {
        resultsCount.textContent = visibleCount;
    }
}

// Clear all filters
function clearStudentFilters() {
    // Reset form inputs and trigger events
    const searchInput = document.querySelector('[x-model="searchQuery"]');
    const classInput = document.querySelector('[x-model="class"]');
    const sectionInput = document.querySelector('[x-model="section"]');
    const statusInput = document.querySelector('[x-model="status"]');
    const dateFromInput = document.querySelector('[x-model="dateFrom"]');
    const dateToInput = document.querySelector('[x-model="dateTo"]');

    if (searchInput) {
        searchInput.value = '';
        searchInput.dispatchEvent(new Event('input'));
    }
    if (classInput) {
        classInput.value = '';
        classInput.dispatchEvent(new Event('change'));
    }
    if (sectionInput) {
        sectionInput.value = '';
        sectionInput.dispatchEvent(new Event('change'));
    }
    if (statusInput) {
        statusInput.value = '';
        statusInput.dispatchEvent(new Event('change'));
    }
    if (dateFromInput) {
        dateFromInput.value = '';
        dateFromInput.dispatchEvent(new Event('change'));
    }
    if (dateToInput) {
        dateToInput.value = '';
        dateToInput.dispatchEvent(new Event('change'));
    }

    // Show all students
    const students = document.querySelectorAll('[data-filterable]');
    students.forEach(student => {
        student.style.display = '';
    });

    // Update results count
    const resultsCount = document.querySelector('[data-results-count]');
    if (resultsCount) {
        resultsCount.textContent = students.length;
    }
}


</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\school-management-system\resources\views/admin/students/index.blade.php ENDPATH**/ ?>