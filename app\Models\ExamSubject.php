<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ExamSubject extends Model
{
    use HasFactory;

    protected $fillable = [
        'exam_id',
        'subject_id',
        'teacher_id',
        'subject_total_marks',
        'subject_passing_marks',
        'subject_start_time',
        'subject_end_time',
        'subject_duration_minutes',
        'subject_instructions',
    ];

    protected $casts = [
        'subject_total_marks' => 'decimal:2',
        'subject_passing_marks' => 'decimal:2',
        'subject_start_time' => 'datetime:H:i',
        'subject_end_time' => 'datetime:H:i',
        'subject_duration_minutes' => 'integer',
    ];

    /**
     * Get the exam.
     */
    public function exam(): BelongsTo
    {
        return $this->belongsTo(Exam::class);
    }

    /**
     * Get the subject.
     */
    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class);
    }

    /**
     * Get the teacher.
     */
    public function teacher(): BelongsTo
    {
        return $this->belongsTo(User::class, 'teacher_id');
    }

    /**
     * Get formatted time range.
     */
    public function getFormattedTimeAttribute(): string
    {
        if ($this->subject_start_time && $this->subject_end_time) {
            return $this->subject_start_time->format('g:i A') . ' - ' . $this->subject_end_time->format('g:i A');
        }
        return 'Time not set';
    }

    /**
     * Get passing percentage.
     */
    public function getPassingPercentageAttribute(): float
    {
        return ($this->subject_passing_marks / $this->subject_total_marks) * 100;
    }

    /**
     * Get duration in hours and minutes.
     */
    public function getFormattedDurationAttribute(): string
    {
        if (!$this->subject_duration_minutes) {
            return 'Duration not set';
        }

        $hours = intval($this->subject_duration_minutes / 60);
        $minutes = $this->subject_duration_minutes % 60;

        if ($hours > 0 && $minutes > 0) {
            return "{$hours}h {$minutes}m";
        } elseif ($hours > 0) {
            return "{$hours}h";
        } else {
            return "{$minutes}m";
        }
    }
}
