@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <x-page-header
        title="Add Grade"
        description="Record a new grade for a student"
        :back-route="route('admin.grading.grades.index')"
        back-label="Back to Grades">
    </x-page-header>

    <!-- Grade Form -->
    <div class="card">
        <form method="POST" action="{{ route('admin.grading.grades.store') }}" class="space-y-6">
            @csrf

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Student Selection -->
                <div>
                    <label for="student_id" class="block text-sm font-medium text-gray-700 mb-1">Student *</label>
                    <select name="student_id" id="student_id" class="form-select" required>
                        <option value="">Select Student</option>
                        @foreach($students as $student)
                            <option value="{{ $student->id }}" {{ old('student_id') == $student->id ? 'selected' : '' }}>
                                {{ $student->full_name }} ({{ $student->student_id }})
                            </option>
                        @endforeach
                    </select>
                    @error('student_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Subject Selection -->
                <div>
                    <label for="subject_id" class="block text-sm font-medium text-gray-700 mb-1">Subject *</label>
                    <select name="subject_id" id="subject_id" class="form-select" required>
                        <option value="">Select Subject</option>
                        @foreach($subjects as $subject)
                            <option value="{{ $subject->id }}" {{ old('subject_id') == $subject->id ? 'selected' : '' }}>
                                {{ $subject->name }} ({{ $subject->code }})
                            </option>
                        @endforeach
                    </select>
                    @error('subject_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Academic Year -->
                <div>
                    <label for="academic_year_id" class="block text-sm font-medium text-gray-700 mb-1">Academic Year *</label>
                    <select name="academic_year_id" id="academic_year_id" class="form-select" required>
                        <option value="">Select Academic Year</option>
                        @foreach($academicYears as $year)
                            <option value="{{ $year->id }}" {{ old('academic_year_id') == $year->id ? 'selected' : '' }}>
                                {{ $year->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('academic_year_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Academic Term -->
                <div>
                    <label for="academic_term_id" class="block text-sm font-medium text-gray-700 mb-1">Academic Term *</label>
                    <select name="academic_term_id" id="academic_term_id" class="form-select" required>
                        <option value="">Select Academic Term</option>
                        @foreach($academicTerms as $term)
                            <option value="{{ $term->id }}" {{ old('academic_term_id') == $term->id ? 'selected' : '' }}>
                                {{ $term->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('academic_term_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Grade Category -->
                <div>
                    <label for="grade_category_id" class="block text-sm font-medium text-gray-700 mb-1">Grade Category *</label>
                    <select name="grade_category_id" id="grade_category_id" class="form-select" required>
                        <option value="">Select Category</option>
                        @foreach($gradeCategories as $category)
                            <option value="{{ $category->id }}" {{ old('grade_category_id') == $category->id ? 'selected' : '' }}>
                                {{ $category->name }} ({{ $category->weight }}%)
                            </option>
                        @endforeach
                    </select>
                    @error('grade_category_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Grade Type -->
                <div>
                    <label for="grade_type" class="block text-sm font-medium text-gray-700 mb-1">Grade Type *</label>
                    <select name="grade_type" id="grade_type" class="form-select" required>
                        <option value="">Select Type</option>
                        <option value="exam" {{ old('grade_type') == 'exam' ? 'selected' : '' }}>Exam</option>
                        <option value="assignment" {{ old('grade_type') == 'assignment' ? 'selected' : '' }}>Assignment</option>
                        <option value="quiz" {{ old('grade_type') == 'quiz' ? 'selected' : '' }}>Quiz</option>
                        <option value="project" {{ old('grade_type') == 'project' ? 'selected' : '' }}>Project</option>
                        <option value="participation" {{ old('grade_type') == 'participation' ? 'selected' : '' }}>Participation</option>
                    </select>
                    @error('grade_type')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Exam (conditional) -->
                <div id="exam_field" style="display: none;">
                    <label for="exam_id" class="block text-sm font-medium text-gray-700 mb-1">Exam</label>
                    <select name="exam_id" id="exam_id" class="form-select">
                        <option value="">Select Exam</option>
                        @foreach($exams as $exam)
                            <option value="{{ $exam->id }}" {{ old('exam_id') == $exam->id ? 'selected' : '' }}>
                                {{ $exam->title }}
                            </option>
                        @endforeach
                    </select>
                    @error('exam_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Assignment (conditional) -->
                <div id="assignment_field" style="display: none;">
                    <label for="assignment_id" class="block text-sm font-medium text-gray-700 mb-1">Assignment</label>
                    <select name="assignment_id" id="assignment_id" class="form-select">
                        <option value="">Select Assignment</option>
                        @foreach($assignments as $assignment)
                            <option value="{{ $assignment->id }}" {{ old('assignment_id') == $assignment->id ? 'selected' : '' }}>
                                {{ $assignment->title }}
                            </option>
                        @endforeach
                    </select>
                    @error('assignment_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Marks Obtained -->
                <div>
                    <label for="marks_obtained" class="block text-sm font-medium text-gray-700 mb-1">Marks Obtained *</label>
                    <input type="number" name="marks_obtained" id="marks_obtained" 
                           class="form-input" step="0.1" min="0" 
                           value="{{ old('marks_obtained') }}" required>
                    @error('marks_obtained')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Total Marks -->
                <div>
                    <label for="total_marks" class="block text-sm font-medium text-gray-700 mb-1">Total Marks *</label>
                    <input type="number" name="total_marks" id="total_marks" 
                           class="form-input" step="0.1" min="0" 
                           value="{{ old('total_marks') }}" required>
                    @error('total_marks')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Grade Letter (optional) -->
                <div>
                    <label for="grade_letter" class="block text-sm font-medium text-gray-700 mb-1">Grade Letter</label>
                    <input type="text" name="grade_letter" id="grade_letter" 
                           class="form-input" maxlength="5" 
                           value="{{ old('grade_letter') }}"
                           placeholder="Auto-calculated if empty">
                    @error('grade_letter')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Grade Point (optional) -->
                <div>
                    <label for="grade_point" class="block text-sm font-medium text-gray-700 mb-1">Grade Point</label>
                    <input type="number" name="grade_point" id="grade_point" 
                           class="form-input" step="0.1" min="0" max="4" 
                           value="{{ old('grade_point') }}"
                           placeholder="Auto-calculated if empty">
                    @error('grade_point')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Remarks -->
            <div>
                <label for="remarks" class="block text-sm font-medium text-gray-700 mb-1">Remarks</label>
                <textarea name="remarks" id="remarks" rows="3" class="form-textarea"
                          placeholder="Optional remarks about this grade">{{ old('remarks') }}</textarea>
                @error('remarks')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Published Status -->
            <div class="flex items-center">
                <input type="checkbox" name="is_published" id="is_published" 
                       class="form-checkbox" value="1" {{ old('is_published') ? 'checked' : '' }}>
                <label for="is_published" class="ml-2 text-sm text-gray-700">
                    Publish grade immediately (students will be able to see this grade)
                </label>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="{{ route('admin.grading.grades.index') }}" class="btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Save Grade
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const gradeTypeSelect = document.getElementById('grade_type');
    const examField = document.getElementById('exam_field');
    const assignmentField = document.getElementById('assignment_field');

    function toggleFields() {
        const gradeType = gradeTypeSelect.value;
        
        examField.style.display = gradeType === 'exam' ? 'block' : 'none';
        assignmentField.style.display = gradeType === 'assignment' ? 'block' : 'none';
    }

    gradeTypeSelect.addEventListener('change', toggleFields);
    toggleFields(); // Initial call
});
</script>
@endsection
