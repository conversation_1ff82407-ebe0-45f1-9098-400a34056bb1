<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo e(config('app.name', 'Laravel')); ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
</head>
<body class="font-sans antialiased bg-gray-50 overflow-hidden" x-data="{
    sidebarOpen: true
}">
    <div class="h-screen flex overflow-hidden">
        <!-- Sidebar -->
        <?php if(auth()->check()): ?>
            <?php echo $__env->make('components.sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <?php endif; ?>

        <!-- Main Content -->
        <main class="flex-1 min-w-0 flex flex-col overflow-hidden <?php if(auth()->check()): ?> ml-64 <?php endif; ?>">
            <div class="flex-1 overflow-y-auto pb-20">
                <div class="px-6 py-6">
                    <?php echo $__env->yieldContent('content'); ?>
                </div>
            </div>
        </main>


    </div>

    <!-- Toast Notifications -->
    <?php echo $__env->make('components.toast', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- Confirmation Modal -->
    <?php echo $__env->make('components.confirmation-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- Additional Scripts -->
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\school-management-system\resources\views/layouts/app.blade.php ENDPATH**/ ?>