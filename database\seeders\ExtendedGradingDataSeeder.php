<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Student;
use App\Models\Guardian;
use App\Models\Teacher;
use App\Models\Section;
use App\Models\ClassModel;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class ExtendedGradingDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create additional sections
        $this->createSections();
        
        // Create additional students
        $this->createAdditionalStudents();
        
        // Create additional teachers
        $this->createAdditionalTeachers();
    }

    private function createSections(): void
    {
        $classes = ClassModel::whereIn('name', ['Form 3', 'Form 4'])->get();
        
        $sectionData = [
            ['name' => 'Science'],
            ['name' => 'Arts'],
            ['name' => 'Commerce'],
        ];

        foreach ($classes as $class) {
            foreach ($sectionData as $section) {
                Section::firstOrCreate([
                    'class_id' => $class->id,
                    'name' => $section['name'],
                ], [
                    'capacity' => 30,
                    'is_active' => true,
                ]);
            }
        }
    }

    private function createAdditionalStudents(): void
    {
        $studentsData = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'student_id' => 'STU003',
                'class' => 'Form 3',
                'section' => 'Science',
                'roll_number' => '003',
                'gender' => 'female',
                'guardian_name' => 'Mr. Chen Wei Ming',
                'guardian_email' => '<EMAIL>',
                'guardian_relationship' => 'Father',
                'guardian_occupation' => 'Accountant',
            ],
            [
                'name' => 'Ahmad Faiz',
                'email' => '<EMAIL>',
                'student_id' => 'STU004',
                'class' => 'Form 3',
                'section' => 'Science',
                'roll_number' => '004',
                'gender' => 'male',
                'guardian_name' => 'Mrs. Siti Aminah',
                'guardian_email' => '<EMAIL>',
                'guardian_relationship' => 'Mother',
                'guardian_occupation' => 'Teacher',
            ],
            [
                'name' => 'Priya Sharma',
                'email' => '<EMAIL>',
                'student_id' => 'STU005',
                'class' => 'Form 4',
                'section' => 'Science',
                'roll_number' => '005',
                'gender' => 'female',
                'guardian_name' => 'Dr. Raj Sharma',
                'guardian_email' => '<EMAIL>',
                'guardian_relationship' => 'Father',
                'guardian_occupation' => 'Doctor',
            ],
            [
                'name' => 'Daniel Wong',
                'email' => '<EMAIL>',
                'student_id' => 'STU006',
                'class' => 'Form 4',
                'section' => 'Science',
                'roll_number' => '006',
                'gender' => 'male',
                'guardian_name' => 'Mrs. Linda Wong',
                'guardian_email' => '<EMAIL>',
                'guardian_relationship' => 'Mother',
                'guardian_occupation' => 'Nurse',
            ],
        ];

        foreach ($studentsData as $data) {
            // Create Guardian User
            $guardianUser = User::create([
                'name' => $data['guardian_name'],
                'email' => $data['guardian_email'],
                'password' => Hash::make('password'),
                'user_type' => 'guardian',
                'phone' => '+6012345' . rand(1000, 9999),
                'address' => rand(100, 999) . ' Sample Street, Kuala Lumpur',
                'is_active' => true,
                'email_verified_at' => now(),
            ]);

            $guardian = Guardian::create([
                'user_id' => $guardianUser->id,
                'relationship' => $data['guardian_relationship'],
                'occupation' => $data['guardian_occupation'],
                'workplace' => 'Sample Workplace',
                'emergency_contact' => '+6012345' . rand(1000, 9999),
                'monthly_income' => rand(5000, 15000),
            ]);

            // Create Student User
            $studentUser = User::create([
                'name' => $data['name'],
                'email' => $data['email'],
                'password' => Hash::make('password'),
                'user_type' => 'student',
                'phone' => '+6012345' . rand(1000, 9999),
                'address' => $guardianUser->address,
                'is_active' => true,
                'email_verified_at' => now(),
            ]);

            $student = Student::create([
                'user_id' => $studentUser->id,
                'student_id' => $data['student_id'],
                'date_of_birth' => now()->subYears(rand(15, 17)),
                'gender' => $data['gender'],
                'class' => $data['class'],
                'section' => $data['section'],
                'roll_number' => $data['roll_number'],
                'admission_date' => now()->subYears(rand(2, 5)),
                'blood_group' => ['A+', 'B+', 'O+', 'AB+'][rand(0, 3)],
                'medical_conditions' => null,
                'emergency_contact' => $guardianUser->phone,
                'guardian_ids' => [$guardian->id],
            ]);

            // Link Guardian and Student
            $guardian->students()->attach($student->id, ['relationship_type' => 'Primary']);
        }
    }

    private function createAdditionalTeachers(): void
    {
        $teachersData = [
            [
                'name' => 'Ms. Jennifer Tan',
                'email' => '<EMAIL>',
                'employee_id' => 'TCH003',
                'qualification' => 'Bachelor of Arts in English',
                'specialization' => 'English Literature',
                'subjects' => ['English Language', 'English Literature'],
                'classes' => ['Form 3', 'Form 4', 'Form 5'],
            ],
            [
                'name' => 'Mr. Kumar Raj',
                'email' => '<EMAIL>',
                'employee_id' => 'TCH004',
                'qualification' => 'Master of Science in Mathematics',
                'specialization' => 'Applied Mathematics',
                'subjects' => ['Mathematics', 'Additional Mathematics'],
                'classes' => ['Form 3', 'Form 4', 'Form 5'],
            ],
            [
                'name' => 'Dr. Fatimah Ali',
                'email' => '<EMAIL>',
                'employee_id' => 'TCH005',
                'qualification' => 'PhD in Biology',
                'specialization' => 'Molecular Biology',
                'subjects' => ['Biology', 'Science'],
                'classes' => ['Form 1', 'Form 2', 'Form 3', 'Form 4', 'Form 5'],
            ],
        ];

        foreach ($teachersData as $data) {
            $teacherUser = User::create([
                'name' => $data['name'],
                'email' => $data['email'],
                'password' => Hash::make('password'),
                'user_type' => 'teacher',
                'phone' => '+6012345' . rand(1000, 9999),
                'address' => rand(100, 999) . ' Teacher Avenue, Kuala Lumpur',
                'is_active' => true,
                'email_verified_at' => now(),
            ]);

            Teacher::create([
                'user_id' => $teacherUser->id,
                'employee_id' => $data['employee_id'],
                'qualification' => $data['qualification'],
                'specialization' => $data['specialization'],
                'hire_date' => now()->subMonths(rand(6, 36)),
                'salary' => rand(3500, 6000),
                'subjects' => $data['subjects'],
                'classes' => $data['classes'],
            ]);
        }
    }
}
