<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class ActivityLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'user_name',
        'action',
        'description',
        'model_type',
        'model_id',
        'properties',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'properties' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user that performed the activity
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to filter by date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope to filter by action
     */
    public function scopeByAction($query, $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope to filter by user
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to search across multiple fields
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function($q) use ($search) {
            $q->where('user_name', 'like', '%' . $search . '%')
              ->orWhere('action', 'like', '%' . $search . '%')
              ->orWhere('description', 'like', '%' . $search . '%')
              ->orWhere('model_type', 'like', '%' . $search . '%');
        });
    }

    /**
     * Get formatted action for display
     */
    public function getFormattedActionAttribute()
    {
        return ucwords(str_replace('_', ' ', $this->action));
    }

    /**
     * Get action badge color based on action type
     */
    public function getActionBadgeColorAttribute()
    {
        $action = strtolower($this->action);

        // Exact matches first
        $exactMatches = [
            'bulk_marked_attendance' => 'blue',
            'marked_attendance' => 'green',
        ];

        if (isset($exactMatches[$action])) {
            return $exactMatches[$action];
        }

        // Then partial matches
        $actionColors = [
            // Attendance actions
            'attendance' => 'purple',

            // CRUD operations
            'created' => 'green',
            'updated' => 'blue',
            'deleted' => 'red',
            'edited' => 'blue',
            'added' => 'green',
            'removed' => 'red',

            // Authentication
            'login' => 'gray',
            'logout' => 'gray',
            'logged in' => 'gray',
            'logged out' => 'gray',

            // Financial
            'payment' => 'yellow',
            'invoice' => 'orange',
            'paid' => 'green',
            'refund' => 'red',

            // Bulk operations
            'bulk' => 'indigo',

            // System actions
            'export' => 'purple',
            'import' => 'blue',
            'backup' => 'gray',
            'restore' => 'yellow',
        ];

        foreach ($actionColors as $key => $color) {
            if (str_contains($action, $key)) {
                return $color;
            }
        }

        return 'gray';
    }

    /**
     * Get model name for display
     */
    public function getModelNameAttribute()
    {
        if (!$this->model_type) {
            return null;
        }

        $modelName = class_basename($this->model_type);
        return ucwords(str_replace('_', ' ', \Illuminate\Support\Str::snake($modelName)));
    }

    /**
     * Log an activity
     */
    public static function log($action, $description, $modelType = null, $modelId = null, $properties = [])
    {
        return self::create([
            'user_id' => auth()->id(),
            'user_name' => auth()->user()->name ?? 'System',
            'action' => $action,
            'description' => $description,
            'model_type' => $modelType,
            'model_id' => $modelId,
            'properties' => $properties,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }

    /**
     * Log attendance activity
     */
    public static function logAttendance($action, $type, $names, $date, $status, $count = 1, $notes = null)
    {
        $description = $count > 1 
            ? "Bulk marked {$count} {$type}(s) as {$status} for {$date}"
            : "Marked {$names} ({$type}) as {$status} for {$date}";

        return self::log(
            $action,
            $description,
            $type === 'student' ? 'App\Models\StudentAttendance' : 'App\Models\TeacherAttendance',
            null,
            [
                'type' => $type,
                'names' => is_array($names) ? $names : [$names],
                'date' => $date,
                'status' => $status,
                'count' => $count,
                'notes' => $notes,
            ]
        );
    }

    /**
     * Log invoice activity
     */
    public static function logInvoice($action, $invoice, $description = null)
    {
        $desc = $description ?: ucfirst($action) . " invoice {$invoice->invoice_number}";
        
        return self::log(
            $action . '_invoice',
            $desc,
            'App\Models\Invoice',
            $invoice->id,
            [
                'invoice_number' => $invoice->invoice_number,
                'student_name' => $invoice->student->user->name ?? null,
                'amount' => $invoice->total_amount,
                'status' => $invoice->status,
            ]
        );
    }

    /**
     * Log payment activity
     */
    public static function logPayment($action, $payment, $description = null)
    {
        $desc = $description ?: ucfirst($action) . " payment for invoice {$payment->invoice->invoice_number}";
        
        return self::log(
            $action . '_payment',
            $desc,
            'App\Models\Payment',
            $payment->id,
            [
                'invoice_number' => $payment->invoice->invoice_number,
                'amount' => $payment->amount,
                'status' => $payment->status,
                'method' => $payment->payment_method,
            ]
        );
    }

    /**
     * Log user activity
     */
    public static function logUser($action, $user, $description = null)
    {
        $desc = $description ?: ucfirst($action) . " user {$user->name}";
        
        return self::log(
            $action . '_user',
            $desc,
            'App\Models\User',
            $user->id,
            [
                'user_name' => $user->name,
                'user_email' => $user->email,
                'user_role' => $user->role,
            ]
        );
    }
}
