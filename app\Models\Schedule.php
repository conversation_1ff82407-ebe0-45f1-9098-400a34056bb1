<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Schedule extends Model
{
    use HasFactory;

    protected $fillable = [
        'academic_year_id',
        'academic_term_id',
        'class_id',
        'section_id',
        'subject_id',
        'teacher_id',
        'time_slot_id',
        'day_of_week',
        'room_number',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the academic year.
     */
    public function academicYear(): BelongsTo
    {
        return $this->belongsTo(AcademicYear::class);
    }

    /**
     * Get the academic term.
     */
    public function academicTerm(): BelongsTo
    {
        return $this->belongsTo(AcademicTerm::class);
    }

    /**
     * Get the class.
     */
    public function class(): BelongsTo
    {
        return $this->belongsTo(ClassModel::class, 'class_id');
    }

    /**
     * Get the school class (alias for class to avoid PHP keyword conflicts).
     */
    public function schoolClass(): BelongsTo
    {
        return $this->belongsTo(ClassModel::class, 'class_id');
    }

    /**
     * Get the section.
     */
    public function section(): BelongsTo
    {
        return $this->belongsTo(Section::class);
    }

    /**
     * Get the subject.
     */
    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class);
    }

    /**
     * Get the teacher.
     */
    public function teacher(): BelongsTo
    {
        return $this->belongsTo(User::class, 'teacher_id');
    }

    /**
     * Get the time slot.
     */
    public function timeSlot(): BelongsTo
    {
        return $this->belongsTo(TimeSlot::class);
    }

    /**
     * Get schedules for a specific class and section.
     */
    public static function forClassSection($classId, $sectionId, $termId = null)
    {
        $query = static::where('class_id', $classId)
                      ->where('section_id', $sectionId)
                      ->where('is_active', true)
                      ->with(['subject', 'teacher', 'timeSlot']);

        if ($termId) {
            $query->where('academic_term_id', $termId);
        } else {
            $currentTerm = AcademicTerm::current();
            if ($currentTerm) {
                $query->where('academic_term_id', $currentTerm->id);
            }
        }

        return $query->orderBy('day_of_week')->orderBy('time_slot_id');
    }

    /**
     * Get schedules for a specific teacher.
     */
    public static function forTeacher($teacherId, $termId = null)
    {
        $query = static::where('teacher_id', $teacherId)
                      ->where('is_active', true)
                      ->with(['class', 'section', 'subject', 'timeSlot']);

        if ($termId) {
            $query->where('academic_term_id', $termId);
        } else {
            $currentTerm = AcademicTerm::current();
            if ($currentTerm) {
                $query->where('academic_term_id', $currentTerm->id);
            }
        }

        return $query->orderBy('day_of_week')->orderBy('time_slot_id');
    }

    /**
     * Get formatted class name.
     */
    public function getClassNameAttribute(): string
    {
        return $this->class->name . ' - ' . $this->section->name;
    }

    /**
     * Get day name.
     */
    public function getDayNameAttribute(): string
    {
        return ucfirst($this->day_of_week);
    }
}
