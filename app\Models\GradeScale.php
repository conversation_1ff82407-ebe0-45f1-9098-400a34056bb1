<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class GradeScale extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'scale_values',
        'is_default',
        'is_active',
    ];

    protected $casts = [
        'scale_values' => 'array',
        'is_default' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get the exams using this grade scale.
     */
    public function exams(): HasMany
    {
        return $this->hasMany(Exam::class);
    }

    /**
     * Get the assignments using this grade scale.
     */
    public function assignments(): HasMany
    {
        return $this->hasMany(Assignment::class);
    }

    /**
     * Get the grades using this scale.
     */
    public function grades(): HasMany
    {
        return $this->hasMany(Grade::class);
    }

    /**
     * Get the default grade scale.
     */
    public static function default()
    {
        return static::where('is_default', true)->where('is_active', true)->first();
    }

    /**
     * Calculate letter grade from percentage.
     */
    public function getLetterGrade(float $percentage): ?string
    {
        if (!$this->scale_values) {
            return null;
        }

        foreach ($this->scale_values as $scale) {
            if ($percentage >= $scale['min'] && $percentage <= $scale['max']) {
                return $scale['grade'] ?? null;
            }
        }

        return null;
    }

    /**
     * Calculate grade points from percentage.
     */
    public function getGradePoints(float $percentage): ?float
    {
        if (!$this->scale_values) {
            return null;
        }

        foreach ($this->scale_values as $scale) {
            if ($percentage >= $scale['min'] && $percentage <= $scale['max']) {
                return isset($scale['points']) ? (float) $scale['points'] : null;
            }
        }

        return null;
    }

    /**
     * Get grade color for UI display.
     */
    public function getGradeColor(float $percentage): string
    {
        if ($percentage >= 90) return 'text-green-600';
        if ($percentage >= 80) return 'text-blue-600';
        if ($percentage >= 70) return 'text-yellow-600';
        if ($percentage >= 60) return 'text-orange-600';
        return 'text-red-600';
    }

    /**
     * Get grade badge color for UI display.
     */
    public function getGradeBadgeColor(float $percentage): string
    {
        if ($percentage >= 90) return 'badge-green';
        if ($percentage >= 80) return 'badge-blue';
        if ($percentage >= 70) return 'badge-yellow';
        if ($percentage >= 60) return 'badge-orange';
        return 'badge-red';
    }

    /**
     * Validate scale values format.
     */
    public function validateScaleValues(): bool
    {
        if (!is_array($this->scale_values)) {
            return false;
        }

        foreach ($this->scale_values as $scale) {
            if (!isset($scale['min'], $scale['max'], $scale['grade'])) {
                return false;
            }
            
            if (!is_numeric($scale['min']) || !is_numeric($scale['max'])) {
                return false;
            }
            
            if ($scale['min'] > $scale['max']) {
                return false;
            }
        }

        return true;
    }
}
