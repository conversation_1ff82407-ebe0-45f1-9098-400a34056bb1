<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Subject;
use App\Models\Assignment;
use App\Models\AcademicYear;
use App\Models\AcademicTerm;
use App\Models\GradeCategory;
use App\Models\GradeScale;
use App\Models\ClassModel;
use App\Models\User;
use Carbon\Carbon;

class SampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->createRequiredData();
        $this->createSampleSubjects();
        $this->createSampleStudents();
        $this->createSampleAssignments();
    }

    private function createRequiredData()
    {
        // Create Academic Year if not exists
        if (AcademicYear::count() == 0) {
            AcademicYear::create([
                'name' => '2024-2025',
                'start_date' => '2024-09-01',
                'end_date' => '2025-06-30',
                'is_current' => true,
                'is_active' => true,
            ]);
        }

        // Create Academic Term if not exists
        if (AcademicTerm::count() == 0) {
            $academicYear = AcademicYear::first();
            AcademicTerm::create([
                'name' => 'First Term',
                'academic_year_id' => $academicYear->id,
                'start_date' => '2024-09-01',
                'end_date' => '2024-12-20',
                'is_current' => true,
                'is_active' => true,
            ]);
        }

        // Create Grade Category if not exists
        if (GradeCategory::count() == 0) {
            GradeCategory::create([
                'name' => 'Assignments',
                'code' => 'ASSIGN',
                'description' => 'Regular assignments and homework',
                'weight_percentage' => 40,
                'color' => '#3b82f6',
                'is_active' => true,
                'sort_order' => 1,
            ]);
        }

        // Create Grade Scale if not exists
        if (GradeScale::count() == 0) {
            GradeScale::create([
                'name' => 'Standard Scale',
                'type' => 'percentage',
                'scale_values' => [
                    ['min' => 90, 'max' => 100, 'grade' => 'A', 'points' => 4.0],
                    ['min' => 80, 'max' => 89, 'grade' => 'B', 'points' => 3.0],
                    ['min' => 70, 'max' => 79, 'grade' => 'C', 'points' => 2.0],
                    ['min' => 60, 'max' => 69, 'grade' => 'D', 'points' => 1.0],
                    ['min' => 0, 'max' => 59, 'grade' => 'F', 'points' => 0.0],
                ],
                'is_default' => true,
                'is_active' => true,
            ]);
        }

        // Create a teacher user if not exists
        if (User::whereHas('teacher')->count() == 0) {
            // Check if user already exists
            $user = User::where('email', '<EMAIL>')->first();
            if (!$user) {
                $user = User::create([
                    'name' => 'John Teacher',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'user_type' => 'teacher',
                    'phone' => '+60123456790',
                    'address' => 'Teacher Address',
                    'is_active' => true,
                    'email_verified_at' => now(),
                ]);
            }

            // Create teacher record if not exists
            if (!\App\Models\Teacher::where('user_id', $user->id)->exists()) {
                \App\Models\Teacher::create([
                    'user_id' => $user->id,
                    'employee_id' => 'TCH001',
                    'qualification' => 'Bachelor of Education',
                    'specialization' => 'Mathematics',
                    'hire_date' => '2020-01-01',
                    'salary' => 50000,
                    'subjects' => ['Mathematics', 'Science'],
                    'classes' => ['Grade 9', 'Grade 10'],
                ]);
            }
        }

        $this->command->info('Required data created successfully!');
    }

    private function createSampleSubjects()
    {
        $subjects = [
            [
                'subject_code' => 'MATH101',
                'name' => 'Mathematics',
                'description' => 'Basic mathematics covering algebra, geometry, and arithmetic',
                'category' => 'Core',
                'credits' => 4,
                'grade_levels' => ['Grade 9', 'Grade 10'],
                'prerequisites' => [],
                'is_active' => true,
            ],
            [
                'subject_code' => 'ENG101',
                'name' => 'English Language',
                'description' => 'English language and literature studies',
                'category' => 'Core',
                'credits' => 3,
                'grade_levels' => ['Grade 9', 'Grade 10', 'Grade 11'],
                'prerequisites' => [],
                'is_active' => true,
            ],
            [
                'subject_code' => 'SCI101',
                'name' => 'General Science',
                'description' => 'Introduction to physics, chemistry, and biology',
                'category' => 'Core',
                'credits' => 4,
                'grade_levels' => ['Grade 9'],
                'prerequisites' => [],
                'is_active' => true,
            ],
            [
                'subject_code' => 'HIST101',
                'name' => 'History',
                'description' => 'World history and social studies',
                'category' => 'Elective',
                'credits' => 2,
                'grade_levels' => ['Grade 10', 'Grade 11'],
                'prerequisites' => [],
                'is_active' => true,
            ],
            [
                'subject_code' => 'ART101',
                'name' => 'Art & Design',
                'description' => 'Creative arts and design fundamentals',
                'category' => 'Elective',
                'credits' => 2,
                'grade_levels' => ['Grade 9', 'Grade 10'],
                'prerequisites' => [],
                'is_active' => false,
            ],
            [
                'subject_code' => 'PE101',
                'name' => 'Physical Education',
                'description' => 'Physical fitness and sports activities',
                'category' => 'Core',
                'credits' => 1,
                'grade_levels' => ['Grade 9', 'Grade 10', 'Grade 11'],
                'prerequisites' => [],
                'is_active' => true,
            ],
        ];

        foreach ($subjects as $subjectData) {
            Subject::firstOrCreate(
                ['subject_code' => $subjectData['subject_code']],
                $subjectData
            );
        }

        $this->command->info('Sample subjects created successfully!');
    }

    private function createSampleStudents()
    {
        // Create a class if not exists
        if (\App\Models\ClassModel::count() == 0) {
            $class = \App\Models\ClassModel::create([
                'name' => 'Grade 9A',
                'level' => 'Grade 9',
                'description' => 'Grade 9 Section A',
                'is_active' => true,
                'sort_order' => 1,
            ]);
        } else {
            $class = \App\Models\ClassModel::first();
        }

        // Create a section if not exists
        if (\App\Models\Section::count() == 0) {
            $section = \App\Models\Section::create([
                'class_id' => $class->id,
                'name' => 'A',
                'capacity' => 30,
                'is_active' => true,
            ]);
        } else {
            $section = \App\Models\Section::first();
        }

        // Create sample students
        $students = [
            [
                'name' => 'John Smith',
                'email' => '<EMAIL>',
                'student_id' => 'STU001',
                'date_of_birth' => '2008-05-15',
                'gender' => 'male',
                'roll_number' => '001',
            ],
            [
                'name' => 'Jane Doe',
                'email' => '<EMAIL>',
                'student_id' => 'STU002',
                'date_of_birth' => '2008-08-22',
                'gender' => 'female',
                'roll_number' => '002',
            ],
            [
                'name' => 'Mike Johnson',
                'email' => '<EMAIL>',
                'student_id' => 'STU003',
                'date_of_birth' => '2008-03-10',
                'gender' => 'male',
                'roll_number' => '003',
            ],
        ];

        foreach ($students as $studentData) {
            // Check if user already exists
            $user = User::where('email', $studentData['email'])->first();
            if (!$user) {
                $user = User::create([
                    'name' => $studentData['name'],
                    'email' => $studentData['email'],
                    'password' => bcrypt('password'),
                    'user_type' => 'student',
                    'phone' => '+60123456789',
                    'address' => 'Student Address',
                    'is_active' => true,
                    'email_verified_at' => now(),
                ]);
            }

            // Create student record if not exists
            $student = \App\Models\Student::where('user_id', $user->id)->first();
            if (!$student) {
                $student = \App\Models\Student::create([
                    'user_id' => $user->id,
                    'student_id' => $studentData['student_id'],
                    'date_of_birth' => $studentData['date_of_birth'],
                    'gender' => $studentData['gender'],
                    'class' => $class->name,
                    'section' => $section->name,
                    'roll_number' => $studentData['roll_number'],
                    'admission_date' => '2024-09-01',
                ]);

                // Create enrollment
                \App\Models\StudentEnrollment::create([
                    'student_id' => $student->id,
                    'class_id' => $class->id,
                    'section_id' => $section->id,
                    'enrollment_date' => '2024-09-01',
                    'is_active' => true,
                ]);
            }
        }

        $this->command->info('Sample students created successfully!');
    }

    private function createSampleAssignments()
    {
        // Get required data
        $academicYear = AcademicYear::first();
        $academicTerm = AcademicTerm::first();
        $gradeCategory = GradeCategory::first();
        $gradeScale = GradeScale::first();
        $class = ClassModel::first();
        $section = \App\Models\Section::first();
        $teacher = User::whereHas('teacher')->first();
        $subjects = Subject::where('is_active', true)->take(3)->get();

        if (!$academicYear || !$academicTerm || !$gradeCategory || !$gradeScale || !$class || !$teacher || $subjects->isEmpty()) {
            $this->command->warn('Required data not found for creating assignments. Please run the main seeders first.');
            return;
        }

        $assignments = [
            [
                'title' => 'Mathematics Quiz 1',
                'assignment_code' => 'MATH_Q1_' . time(),
                'description' => 'Basic algebra and arithmetic quiz',
                'instructions' => 'Complete all questions within 45 minutes. Show your work for partial credit.',
                'subject_id' => $subjects->where('subject_code', 'MATH101')->first()?->id ?? $subjects->first()->id,
                'total_marks' => 50,
                'passing_marks' => 30,
                'assigned_date' => Carbon::now()->subDays(7),
                'due_date' => Carbon::now()->addDays(3),
                'due_time' => '23:59',
                'submission_type' => 'online',
                'status' => 'active',
                'is_published' => true,
                'allow_late_submission' => true,
                'late_penalty_percentage' => 10,
                'max_attempts' => 2,
            ],
            [
                'title' => 'English Essay Assignment',
                'assignment_code' => 'ENG_ESS1_' . time(),
                'description' => 'Write a 500-word essay on a given topic',
                'instructions' => 'Choose one of the provided topics and write a well-structured essay. Include introduction, body paragraphs, and conclusion.',
                'subject_id' => $subjects->where('subject_code', 'ENG101')->first()?->id ?? $subjects->skip(1)->first()->id,
                'total_marks' => 100,
                'passing_marks' => 60,
                'assigned_date' => Carbon::now()->subDays(5),
                'due_date' => Carbon::now()->addDays(7),
                'due_time' => '18:00',
                'submission_type' => 'both',
                'status' => 'active',
                'is_published' => true,
                'allow_late_submission' => false,
                'late_penalty_percentage' => 0,
                'max_attempts' => 1,
            ],
            [
                'title' => 'Science Lab Report',
                'assignment_code' => 'SCI_LAB1_' . time(),
                'description' => 'Laboratory experiment report on chemical reactions',
                'instructions' => 'Document your experiment process, observations, and conclusions. Include diagrams where necessary.',
                'subject_id' => $subjects->where('subject_code', 'SCI101')->first()?->id ?? $subjects->skip(2)->first()->id,
                'total_marks' => 75,
                'passing_marks' => 45,
                'assigned_date' => Carbon::now()->subDays(3),
                'due_date' => Carbon::now()->addDays(10),
                'due_time' => '16:00',
                'submission_type' => 'offline',
                'status' => 'draft',
                'is_published' => false,
                'allow_late_submission' => true,
                'late_penalty_percentage' => 5,
                'max_attempts' => 1,
            ],
        ];

        foreach ($assignments as $assignmentData) {
            Assignment::create(array_merge($assignmentData, [
                'academic_year_id' => $academicYear->id,
                'academic_term_id' => $academicTerm->id,
                'grade_category_id' => $gradeCategory->id,
                'grade_scale_id' => $gradeScale->id,
                'class_id' => $class->id,
                'section_id' => $section->id,
                'teacher_id' => $teacher->id,
            ]));
        }

        $this->command->info('Sample assignments created successfully!');
    }
}
