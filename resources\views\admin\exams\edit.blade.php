@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <x-page-header
        title="Edit Exam"
        description="Update exam information and settings"
        :back-route="route('admin.grading.exams.show', $exam)"
        back-label="Back to Exam" />

    <!-- Edit Form -->
    <div class="card">
        <form method="POST" action="{{ route('admin.grading.exams.update', $exam) }}" class="space-y-6">
            @csrf
            @method('PUT')
            
            <div class="card-body space-y-6">
                <!-- Basic Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="title" class="form-label">Exam Title</label>
                        <input type="text" 
                               id="title" 
                               name="title" 
                               value="{{ old('title', $exam->title) }}" 
                               class="form-input @error('title') border-red-300 @enderror" 
                               required>
                        @error('title')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="exam_code" class="form-label">Exam Code</label>
                        <input type="text" 
                               id="exam_code" 
                               name="exam_code" 
                               value="{{ old('exam_code', $exam->exam_code) }}" 
                               class="form-input @error('exam_code') border-red-300 @enderror" 
                               required>
                        @error('exam_code')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Academic Information -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="academic_year_id" class="form-label">Academic Year</label>
                        <select id="academic_year_id" 
                                name="academic_year_id" 
                                class="form-select @error('academic_year_id') border-red-300 @enderror" 
                                required>
                            <option value="">Select Academic Year</option>
                            @foreach($academicYears as $year)
                                <option value="{{ $year->id }}" {{ old('academic_year_id', $exam->academic_year_id) == $year->id ? 'selected' : '' }}>
                                    {{ $year->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('academic_year_id')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="academic_term_id" class="form-label">Academic Term</label>
                        <select id="academic_term_id" 
                                name="academic_term_id" 
                                class="form-select @error('academic_term_id') border-red-300 @enderror" 
                                required>
                            <option value="">Select Academic Term</option>
                            @foreach($academicTerms as $term)
                                <option value="{{ $term->id }}" {{ old('academic_term_id', $exam->academic_term_id) == $term->id ? 'selected' : '' }}>
                                    {{ $term->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('academic_term_id')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="grade_category_id" class="form-label">Grade Category</label>
                        <select id="grade_category_id" 
                                name="grade_category_id" 
                                class="form-select @error('grade_category_id') border-red-300 @enderror" 
                                required>
                            <option value="">Select Grade Category</option>
                            @foreach($gradeCategories as $category)
                                <option value="{{ $category->id }}" {{ old('grade_category_id', $exam->grade_category_id) == $category->id ? 'selected' : '' }}>
                                    {{ $category->name }} ({{ $category->weight }}%)
                                </option>
                            @endforeach
                        </select>
                        @error('grade_category_id')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Exam Details -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="exam_date" class="form-label">Exam Date</label>
                        <input type="date" 
                               id="exam_date" 
                               name="exam_date" 
                               value="{{ old('exam_date', $exam->exam_date->format('Y-m-d')) }}" 
                               class="form-input @error('exam_date') border-red-300 @enderror" 
                               required>
                        @error('exam_date')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="start_time" class="form-label">Start Time</label>
                        <input type="time" 
                               id="start_time" 
                               name="start_time" 
                               value="{{ old('start_time', $exam->start_time ? $exam->start_time->format('H:i') : '') }}" 
                               class="form-input @error('start_time') border-red-300 @enderror">
                        @error('start_time')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="duration_minutes" class="form-label">Duration (minutes)</label>
                        <input type="number" 
                               id="duration_minutes" 
                               name="duration_minutes" 
                               value="{{ old('duration_minutes', $exam->duration_minutes) }}" 
                               class="form-input @error('duration_minutes') border-red-300 @enderror" 
                               min="1">
                        @error('duration_minutes')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Target Classes -->
                <div>
                    <label class="form-label">Target Classes</label>
                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 mt-2">
                        @foreach($classes as $class)
                            <div class="flex items-center">
                                <input type="checkbox" 
                                       id="class_{{ $class->id }}" 
                                       name="target_classes[]" 
                                       value="{{ $class->id }}"
                                       {{ in_array($class->id, old('target_classes', $exam->target_classes ?? [])) ? 'checked' : '' }}
                                       class="form-checkbox">
                                <label for="class_{{ $class->id }}" class="ml-2 text-sm text-gray-700">{{ $class->name }}</label>
                            </div>
                        @endforeach
                    </div>
                    @error('target_classes')
                        <p class="form-error">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Grading Settings -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="grade_scale_id" class="form-label">Grade Scale</label>
                        <select id="grade_scale_id" 
                                name="grade_scale_id" 
                                class="form-select @error('grade_scale_id') border-red-300 @enderror">
                            <option value="">Select Grade Scale</option>
                            @foreach($gradeScales as $scale)
                                <option value="{{ $scale->id }}" {{ old('grade_scale_id', $exam->grade_scale_id) == $scale->id ? 'selected' : '' }}>
                                    {{ $scale->name }} ({{ ucfirst($scale->type) }})
                                </option>
                            @endforeach
                        </select>
                        @error('grade_scale_id')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="total_marks" class="form-label">Total Marks</label>
                        <input type="number" 
                               id="total_marks" 
                               name="total_marks" 
                               value="{{ old('total_marks', $exam->total_marks) }}" 
                               class="form-input @error('total_marks') border-red-300 @enderror" 
                               min="1" 
                               step="0.01">
                        @error('total_marks')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Status and Publishing -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="status" class="form-label">Status</label>
                        <select id="status" 
                                name="status" 
                                class="form-select @error('status') border-red-300 @enderror" 
                                required>
                            <option value="draft" {{ old('status', $exam->status) === 'draft' ? 'selected' : '' }}>Draft</option>
                            <option value="scheduled" {{ old('status', $exam->status) === 'scheduled' ? 'selected' : '' }}>Scheduled</option>
                            <option value="ongoing" {{ old('status', $exam->status) === 'ongoing' ? 'selected' : '' }}>Ongoing</option>
                            <option value="completed" {{ old('status', $exam->status) === 'completed' ? 'selected' : '' }}>Completed</option>
                            <option value="cancelled" {{ old('status', $exam->status) === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                        </select>
                        @error('status')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="flex items-center pt-6">
                        <input type="checkbox" 
                               id="is_published" 
                               name="is_published" 
                               value="1"
                               {{ old('is_published', $exam->is_published) ? 'checked' : '' }}
                               class="form-checkbox">
                        <label for="is_published" class="ml-2 text-sm text-gray-700">Publish exam (visible to students)</label>
                    </div>
                </div>

                <!-- Exam Subjects -->
                <div class="col-span-2">
                    <div class="border border-gray-200 rounded-lg p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="text-lg font-medium text-gray-900">Exam Subjects</h4>
                            <button type="button" id="add-subject" class="btn-secondary">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Add Subject
                            </button>
                        </div>

                        <div id="subjects-container" class="space-y-4">
                            <!-- Existing subjects will be loaded here -->
                        </div>

                        @error('subjects')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="form-label">Description</label>
                    <textarea id="description"
                              name="description"
                              rows="4"
                              class="form-textarea @error('description') border-red-300 @enderror"
                              placeholder="Enter exam description, instructions, or notes...">{{ old('description', $exam->description) }}</textarea>
                    @error('description')
                        <p class="form-error">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Form Actions -->
                <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                    <a href="{{ route('admin.grading.exams.show', $exam) }}" class="btn-cancel">
                        Cancel
                    </a>
                    <button type="submit" class="btn-primary">
                        Update Exam
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    let subjectIndex = 0;
    const subjectsContainer = document.getElementById('subjects-container');
    const addSubjectBtn = document.getElementById('add-subject');

    // Load existing subjects
    const existingSubjects = @json($exam->examSubjects ?? []);

    if (existingSubjects.length > 0) {
        existingSubjects.forEach((examSubject, index) => {
            addSubject(examSubject, index);
        });
    } else {
        addSubject();
    }

    addSubjectBtn.addEventListener('click', () => addSubject());

    function addSubject(existingData = null, index = null) {
        const currentIndex = index !== null ? index : subjectIndex;
        const subjectHtml = `
            <div class="subject-item border border-gray-200 rounded-lg p-4" data-index="${currentIndex}">
                <div class="flex items-center justify-between mb-4">
                    <h5 class="text-sm font-medium text-gray-900">Subject ${currentIndex + 1}</h5>
                    <button type="button" class="remove-subject text-red-600 hover:text-red-800" ${currentIndex === 0 && !existingData ? 'style="display: none;"' : ''}>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Subject *</label>
                        <select name="subjects[${currentIndex}][subject_id]" class="form-select" required>
                            <option value="">Select Subject</option>
                            @foreach($subjects ?? [] as $subject)
                                <option value="{{ $subject->id }}" ${existingData && existingData.subject_id == {{ $subject->id }} ? 'selected' : ''}>
                                    {{ $subject->name }} ({{ $subject->subject_code }})
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Teacher</label>
                        <select name="subjects[${currentIndex}][teacher_id]" class="form-select">
                            <option value="">Select Teacher</option>
                            @foreach($teachers ?? [] as $teacher)
                                <option value="{{ $teacher->id }}" ${existingData && existingData.teacher_id == {{ $teacher->id }} ? 'selected' : ''}>
                                    {{ $teacher->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Total Marks *</label>
                        <input type="number" name="subjects[${currentIndex}][subject_total_marks]"
                               class="form-input" min="1" step="0.1" required
                               value="${existingData ? existingData.subject_total_marks : ''}">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Passing Marks *</label>
                        <input type="number" name="subjects[${currentIndex}][subject_passing_marks]"
                               class="form-input" min="0" step="0.1" required
                               value="${existingData ? existingData.subject_passing_marks : ''}">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Start Time</label>
                        <input type="time" name="subjects[${currentIndex}][subject_start_time]" class="form-input"
                               value="${existingData && existingData.subject_start_time ? existingData.subject_start_time.substring(0, 5) : ''}">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">End Time</label>
                        <input type="time" name="subjects[${currentIndex}][subject_end_time]" class="form-input"
                               value="${existingData && existingData.subject_end_time ? existingData.subject_end_time.substring(0, 5) : ''}">
                    </div>

                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Subject Instructions</label>
                        <textarea name="subjects[${currentIndex}][subject_instructions]"
                                  class="form-textarea" rows="2"
                                  placeholder="Specific instructions for this subject">${existingData ? existingData.subject_instructions || '' : ''}</textarea>
                    </div>
                </div>
            </div>
        `;

        subjectsContainer.insertAdjacentHTML('beforeend', subjectHtml);
        if (index === null) subjectIndex++;

        // Add event listener to remove button
        const removeButtons = subjectsContainer.querySelectorAll('.remove-subject');
        removeButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                this.closest('.subject-item').remove();
                updateSubjectNumbers();
            });
        });
    }

    function updateSubjectNumbers() {
        const subjects = subjectsContainer.querySelectorAll('.subject-item');
        subjects.forEach((subject, index) => {
            const title = subject.querySelector('h5');
            title.textContent = `Subject ${index + 1}`;

            // Show/hide remove button for first item
            const removeBtn = subject.querySelector('.remove-subject');
            if (index === 0 && subjects.length === 1) {
                removeBtn.style.display = 'none';
            } else {
                removeBtn.style.display = 'block';
            }
        });
    }
});
</script>
@endpush
