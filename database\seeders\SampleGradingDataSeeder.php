<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Exam;
use App\Models\ExamSubject;
use App\Models\Assignment;
use App\Models\AssignmentSubmission;
use App\Models\Grade;
use App\Models\GradeReport;
use App\Models\GradeCategory;
use App\Models\GradeScale;
use App\Models\AcademicYear;
use App\Models\AcademicTerm;
use App\Models\Subject;
use App\Models\Student;
use App\Models\User;
use App\Models\ClassModel;
use App\Models\Section;
use Carbon\Carbon;

class SampleGradingDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get required data
        $academicYear = AcademicYear::where('is_current', true)->first();
        $currentTerm = AcademicTerm::where('is_current', true)->first();
        $gradeScale = GradeScale::where('is_default', true)->first();
        $students = Student::with('user')->get();
        $teachers = User::where('user_type', 'teacher')->get();
        $subjects = Subject::whereIn('subject_code', ['ENG001', 'MATH001', 'SCI001', 'PHY001', 'CHEM001'])->get();
        $gradeCategories = GradeCategory::all();

        if (!$academicYear || !$currentTerm || !$gradeScale || $students->isEmpty() || $teachers->isEmpty() || $subjects->isEmpty()) {
            $this->command->info('Missing required data. Skipping grading data creation.');
            return;
        }

        $this->command->info('Creating sample assessment data...');

        // Create sample exams
        $this->createSampleExams($academicYear, $currentTerm, $gradeScale, $subjects, $teachers);
        
        // Create sample assignments
        $this->createSampleAssignments($academicYear, $currentTerm, $gradeScale, $subjects, $teachers, $students);
        
        // Create sample grades
        $this->createSampleGrades($academicYear, $currentTerm, $gradeScale, $students, $subjects, $teachers, $gradeCategories);
        
        // Create sample grade reports
        $this->createSampleGradeReports($academicYear, $currentTerm, $students);

        $this->command->info('Sample grading data created successfully!');
    }

    private function createSampleExams($academicYear, $currentTerm, $gradeScale, $subjects, $teachers): void
    {
        $examCategory = GradeCategory::where('code', 'TEST')->first();
        $finalCategory = GradeCategory::where('code', 'FINAL')->first();
        
        if (!$examCategory || !$finalCategory) return;

        // Create Mid-Term Exam
        $examCode = 'MTE-' . $currentTerm->id . '-' . time();
        $midTermExam = Exam::firstOrCreate(
            ['exam_code' => $examCode],
            [
                'title' => 'Mid-Term Examination',
                'description' => 'Mid-term examination for ' . $currentTerm->name,
                'academic_year_id' => $academicYear->id,
                'academic_term_id' => $currentTerm->id,
                'grade_category_id' => $examCategory->id,
                'grade_scale_id' => $gradeScale->id,
                'exam_date' => Carbon::now()->subDays(15),
                'start_time' => '08:00',
                'end_time' => '12:00',
                'duration_minutes' => 240,
                'total_marks' => 100,
                'passing_marks' => 50,
                'status' => 'completed',
                'instructions' => 'Please read all instructions carefully. Answer all questions.',
                'target_classes' => ['Form 3', 'Form 4'],
                'created_by' => $teachers->first()->id,
                'is_published' => true,
                'published_at' => Carbon::now()->subDays(5),
            ]
        );

        // Create exam subjects for the exam
        foreach ($subjects->take(4) as $index => $subject) {
            $teacher = $teachers->get($index % $teachers->count());
            
            ExamSubject::create([
                'exam_id' => $midTermExam->id,
                'subject_id' => $subject->id,
                'teacher_id' => $teacher->id,
                'subject_total_marks' => 100,
                'subject_passing_marks' => 50,
                'subject_start_time' => Carbon::createFromFormat('H:i', '08:00')->addHours($index)->format('H:i'),
                'subject_end_time' => Carbon::createFromFormat('H:i', '08:00')->addHours($index + 2)->format('H:i'),
                'subject_duration_minutes' => 120,
                'subject_instructions' => 'Answer all questions for ' . $subject->name,
            ]);
        }
    }

    private function createSampleAssignments($academicYear, $currentTerm, $gradeScale, $subjects, $teachers, $students): void
    {
        $assignmentCategory = GradeCategory::where('code', 'ASSIGN')->first();
        
        if (!$assignmentCategory) return;

        // Get classes
        $classes = ClassModel::whereIn('name', ['Form 3', 'Form 4'])->get();
        if ($classes->isEmpty()) return;

        $assignmentData = [
            [
                'title' => 'Essay Writing Assignment',
                'subject_code' => 'ENG001',
                'description' => 'Write a 500-word essay on "The Importance of Education"',
                'total_marks' => 50,
                'due_days_ago' => 10,
                'status' => 'completed',
            ],
            [
                'title' => 'Mathematics Problem Set',
                'subject_code' => 'MATH001',
                'description' => 'Complete exercises 1-20 from Chapter 5',
                'total_marks' => 40,
                'due_days_ago' => 7,
                'status' => 'completed',
            ],
        ];

        foreach ($assignmentData as $index => $data) {
            $subject = $subjects->where('subject_code', $data['subject_code'])->first();
            if (!$subject) continue;

            $teacher = $teachers->get($index % $teachers->count());
            $class = $classes->first();

            $assignmentCode = 'ASG-' . strtoupper(substr($data['subject_code'], 0, 3)) . '-' . time() . '-' . ($index + 1);
            $assignment = Assignment::firstOrCreate(
                ['assignment_code' => $assignmentCode],
                [
                    'title' => $data['title'],
                    'description' => $data['description'],
                    'academic_year_id' => $academicYear->id,
                    'academic_term_id' => $currentTerm->id,
                    'subject_id' => $subject->id,
                    'class_id' => $class->id,
                    'teacher_id' => $teacher->id,
                    'grade_category_id' => $assignmentCategory->id,
                    'grade_scale_id' => $gradeScale->id,
                    'assigned_date' => Carbon::now()->subDays(abs($data['due_days_ago']) + 7),
                    'due_date' => Carbon::now()->subDays($data['due_days_ago']),
                    'due_time' => '23:59',
                    'total_marks' => $data['total_marks'],
                    'instructions' => $data['description'],
                    'allow_late_submission' => true,
                    'late_penalty_percentage' => 10,
                    'status' => $data['status'],
                    'is_published' => true,
                ]
            );

            // Create submissions for completed assignments
            if ($data['status'] === 'completed') {
                foreach ($students->take(2) as $student) {
                    AssignmentSubmission::create([
                        'assignment_id' => $assignment->id,
                        'student_id' => $student->id,
                        'submission_text' => 'Sample submission for ' . $data['title'],
                        'submitted_at' => Carbon::now()->subDays($data['due_days_ago'] - 2),
                        'is_late' => false,
                        'teacher_feedback' => 'Good work! Keep it up.',
                        'status' => 'graded',
                    ]);
                }
            }
        }
    }

    private function createSampleGrades($academicYear, $currentTerm, $gradeScale, $students, $subjects, $teachers, $gradeCategories): void
    {
        // Sample grade data for different assessment types
        $gradeData = [
            // Student 1 - Good performer
            [
                'student_index' => 0,
                'grades' => [
                    ['subject_code' => 'ENG001', 'category_code' => 'QUIZ', 'marks' => 85, 'total' => 100],
                    ['subject_code' => 'ENG001', 'category_code' => 'ASSIGN', 'marks' => 42, 'total' => 50],
                    ['subject_code' => 'MATH001', 'category_code' => 'QUIZ', 'marks' => 92, 'total' => 100],
                    ['subject_code' => 'MATH001', 'category_code' => 'ASSIGN', 'marks' => 36, 'total' => 40],
                    ['subject_code' => 'SCI001', 'category_code' => 'QUIZ', 'marks' => 80, 'total' => 100],
                ]
            ],
            // Student 2 - Average performer
            [
                'student_index' => 1,
                'grades' => [
                    ['subject_code' => 'ENG001', 'category_code' => 'QUIZ', 'marks' => 72, 'total' => 100],
                    ['subject_code' => 'ENG001', 'category_code' => 'ASSIGN', 'marks' => 38, 'total' => 50],
                    ['subject_code' => 'MATH001', 'category_code' => 'QUIZ', 'marks' => 78, 'total' => 100],
                    ['subject_code' => 'MATH001', 'category_code' => 'ASSIGN', 'marks' => 32, 'total' => 40],
                    ['subject_code' => 'SCI001', 'category_code' => 'QUIZ', 'marks' => 70, 'total' => 100],
                ]
            ],
        ];

        foreach ($gradeData as $studentData) {
            if (!isset($students[$studentData['student_index']])) continue;
            
            $student = $students[$studentData['student_index']];
            
            foreach ($studentData['grades'] as $gradeInfo) {
                $subject = $subjects->where('subject_code', $gradeInfo['subject_code'])->first();
                $category = $gradeCategories->where('code', $gradeInfo['category_code'])->first();
                
                if (!$subject || !$category) continue;
                
                $teacher = $teachers->random();
                $percentage = ($gradeInfo['marks'] / $gradeInfo['total']) * 100;
                
                // Calculate grade letter based on percentage
                $gradeLetter = $this->calculateGradeLetter($percentage);
                $gradePoint = $this->calculateGradePoint($percentage);
                
                Grade::create([
                    'student_id' => $student->id,
                    'academic_year_id' => $academicYear->id,
                    'academic_term_id' => $currentTerm->id,
                    'subject_id' => $subject->id,
                    'grade_category_id' => $category->id,
                    'grade_scale_id' => $gradeScale->id,
                    'teacher_id' => $teacher->id,
                    'recorded_by' => $teacher->id,
                    'grade_type' => strtolower($gradeInfo['category_code']) === 'assign' ? 'assignment' : strtolower($gradeInfo['category_code']),
                    'marks_obtained' => $gradeInfo['marks'],
                    'total_marks' => $gradeInfo['total'],
                    'percentage' => round($percentage, 2),
                    'grade_letter' => $gradeLetter,
                    'grade_point' => $gradePoint,
                    'is_passed' => $percentage >= 50,
                    'is_published' => true,
                    'status' => 'published',
                    'teacher_comments' => $this->getRandomComment($percentage),
                    'graded_at' => Carbon::now()->subDays(rand(1, 10)),
                    'published_at' => Carbon::now()->subDays(rand(1, 5)),
                ]);
            }
        }
    }

    private function createSampleGradeReports($academicYear, $currentTerm, $students): void
    {
        foreach ($students->take(2) as $index => $student) {
            // Calculate overall performance
            $grades = Grade::where('student_id', $student->id)
                          ->where('academic_term_id', $currentTerm->id)
                          ->get();

            if ($grades->isEmpty()) continue;

            $totalPercentage = $grades->avg('percentage');
            $overallGrade = $this->calculateGradeLetter($totalPercentage);
            $overallGPA = $this->calculateGradePoint($totalPercentage);

            // Group grades by subject
            $subjectGrades = $grades->groupBy('subject_id')->map(function ($subjectGrades) {
                return [
                    'subject_name' => $subjectGrades->first()->subject->name ?? 'Unknown',
                    'average_percentage' => round($subjectGrades->avg('percentage'), 2),
                    'grade_letter' => $this->calculateGradeLetter($subjectGrades->avg('percentage')),
                    'assessments' => $subjectGrades->map(function ($grade) {
                        return [
                            'type' => $grade->grade_type,
                            'marks' => $grade->marks_obtained,
                            'total' => $grade->total_marks,
                            'percentage' => $grade->percentage,
                            'grade' => $grade->grade_letter,
                        ];
                    })->toArray()
                ];
            })->toArray();

            GradeReport::create([
                'student_id' => $student->id,
                'academic_year_id' => $academicYear->id,
                'academic_term_id' => $currentTerm->id,
                'report_type' => 'term_report',
                'subject_grades' => $subjectGrades,
                'overall_percentage' => round($totalPercentage, 2),
                'overall_gpa' => $overallGPA,
                'overall_grade' => $overallGrade,
                'class_rank' => $index + 1,
                'total_students' => $students->count(),
                'teacher_comments' => $this->getDetailedComment($totalPercentage),
                'principal_comments' => 'Keep up the good work and continue to strive for excellence.',
                'attendance_days_present' => 85,
                'attendance_total_days' => 90,
                'attendance_percentage' => 94.44,
                'status' => 'published',
                'published_at' => Carbon::now()->subDays(2),
                'generated_by' => 1,
            ]);
        }
    }

    private function calculateGradeLetter($percentage): string
    {
        if ($percentage >= 97) return 'A+';
        if ($percentage >= 93) return 'A';
        if ($percentage >= 90) return 'A-';
        if ($percentage >= 87) return 'B+';
        if ($percentage >= 83) return 'B';
        if ($percentage >= 80) return 'B-';
        if ($percentage >= 77) return 'C+';
        if ($percentage >= 73) return 'C';
        if ($percentage >= 70) return 'C-';
        if ($percentage >= 67) return 'D+';
        if ($percentage >= 63) return 'D';
        if ($percentage >= 60) return 'D-';
        return 'F';
    }

    private function calculateGradePoint($percentage): float
    {
        if ($percentage >= 97) return 4.0;
        if ($percentage >= 93) return 4.0;
        if ($percentage >= 90) return 3.7;
        if ($percentage >= 87) return 3.3;
        if ($percentage >= 83) return 3.0;
        if ($percentage >= 80) return 2.7;
        if ($percentage >= 77) return 2.3;
        if ($percentage >= 73) return 2.0;
        if ($percentage >= 70) return 1.7;
        if ($percentage >= 67) return 1.3;
        if ($percentage >= 63) return 1.0;
        if ($percentage >= 60) return 0.7;
        return 0.0;
    }

    private function getRandomComment($percentage): string
    {
        if ($percentage >= 90) {
            $comments = [
                'Excellent work! Outstanding performance.',
                'Demonstrates exceptional understanding.',
                'Consistently produces high-quality work.',
                'Shows excellent grasp of concepts.'
            ];
        } elseif ($percentage >= 80) {
            $comments = [
                'Good progress and solid understanding.',
                'Making good progress in the subject.',
                'Shows good grasp of the material.',
                'Consistent effort and good results.'
            ];
        } elseif ($percentage >= 70) {
            $comments = [
                'Satisfactory work with room for improvement.',
                'Shows understanding but needs more practice.',
                'Good effort, keep working hard.',
                'Making steady progress.'
            ];
        } elseif ($percentage >= 60) {
            $comments = [
                'Needs additional support and practice.',
                'Would benefit from extra help.',
                'Shows potential but needs more effort.',
                'Requires more focus and practice.'
            ];
        } else {
            $comments = [
                'Requires immediate intervention and support.',
                'Needs significant improvement.',
                'Must put in more effort to succeed.',
                'Requires additional tutoring and support.'
            ];
        }

        return $comments[array_rand($comments)];
    }

    private function getDetailedComment($percentage): string
    {
        if ($percentage >= 85) {
            return 'Excellent academic performance throughout the term. Shows strong understanding of concepts and consistently produces high-quality work. Continue to maintain this excellent standard.';
        } elseif ($percentage >= 75) {
            return 'Good academic performance with solid understanding of most concepts. Shows consistent effort and engagement. With continued focus, can achieve even better results.';
        } elseif ($percentage >= 65) {
            return 'Satisfactory performance with room for improvement. Shows understanding of basic concepts but needs to work on application and problem-solving skills. Encourage more practice and study.';
        } else {
            return 'Performance below expectations. Requires additional support and intervention to meet learning objectives. Recommend extra tutoring and closer monitoring of progress.';
        }
    }
}
