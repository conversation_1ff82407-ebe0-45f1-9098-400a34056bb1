<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Subject;
use App\Models\ClassModel;
use App\Models\StudentEnrollment;
use App\Models\TeacherSubject;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SubjectController extends Controller
{
    /**
     * Display a listing of subjects
     */
    public function index(Request $request)
    {
        $query = Subject::query();

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('subject_code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('category', 'like', "%{$search}%");
            });
        }

        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        if ($request->filled('grade_level')) {
            $query->whereJsonContains('grade_levels', $request->grade_level);
        }

        $subjects = $query->orderBy('name')->paginate(20);

        // Manually add counts to avoid relationship issues
        $subjects->getCollection()->transform(function ($subject) {
            // Count classes using direct database query to avoid relationship issues
            $subject->classes_count = \DB::table('class_subjects')
                ->where('subject_id', $subject->id)
                ->count();

            // Count teachers using direct database query
            $subject->teachers_count = \DB::table('teacher_subjects')
                ->where('subject_id', $subject->id)
                ->distinct('teacher_id')
                ->count('teacher_id');

            return $subject;
        });

        // Get filter options
        $categories = Subject::distinct()->pluck('category')->filter()->sort();
        $gradeLevels = ClassModel::active()->ordered()->pluck('name');

        // Statistics
        $stats = [
            'total_subjects' => Subject::count(),
            'active_subjects' => Subject::active()->count(),
            'core_subjects' => Subject::where('category', 'Core')->count(),
            'elective_subjects' => Subject::where('category', 'Elective')->count(),
        ];

        return view('admin.subjects.index', compact('subjects', 'categories', 'gradeLevels', 'stats'));
    }

    /**
     * Show the form for creating a new subject
     */
    public function create()
    {
        $classes = ClassModel::active()->ordered()->get();
        $existingSubjects = Subject::active()->orderBy('name')->get();
        
        return view('admin.subjects.create', compact('classes', 'existingSubjects'));
    }

    /**
     * Store a newly created subject
     */
    public function store(Request $request)
    {
        $request->validate([
            'subject_code' => 'required|string|max:20|unique:subjects',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category' => 'required|in:Core,Elective,Extra-curricular',
            'credits' => 'required|integer|min:1|max:10',
            'grade_levels' => 'nullable|array',
            'grade_levels.*' => 'string',
            'prerequisites' => 'nullable|array',
            'prerequisites.*' => 'exists:subjects,id',
            'is_active' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            $subject = Subject::create([
                'subject_code' => $request->subject_code,
                'name' => $request->name,
                'description' => $request->description,
                'category' => $request->category,
                'credits' => $request->credits,
                'grade_levels' => $request->grade_levels,
                'prerequisites' => $request->prerequisites,
                'is_active' => $request->boolean('is_active', true),
            ]);

            // Log activity
            ActivityLog::log(
                'created_subject',
                "Created subject: {$subject->name} ({$subject->subject_code})",
                'App\Models\Subject',
                $subject->id
            );

            DB::commit();

            return redirect()->route('admin.academic.subjects.index')
                           ->with('success', 'Subject created successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'Error creating subject: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified subject
     */
    public function show(Subject $subject)
    {
        // Load teachers relationship (this one works fine)
        $subject->load(['teachers.user']);

        // Load classes manually to avoid relationship issues
        $classes = \DB::table('class_subjects')
            ->join('classes', 'classes.id', '=', 'class_subjects.class_id')
            ->where('class_subjects.subject_id', $subject->id)
            ->select('classes.*', 'class_subjects.is_mandatory', 'class_subjects.hours_per_week')
            ->get();

        $subject->classes_manual = $classes;

        // Load prerequisite and dependent subjects manually since they're not proper relationships
        $prerequisiteSubjects = $subject->prerequisiteSubjects();
        $dependentSubjects = $subject->dependentSubjects();

        return view('admin.subjects.show', compact('subject', 'prerequisiteSubjects', 'dependentSubjects'));
    }

    /**
     * Show the form for editing the specified subject
     */
    public function edit(Subject $subject)
    {
        $classes = ClassModel::active()->ordered()->get();
        $existingSubjects = Subject::active()->where('id', '!=', $subject->id)->orderBy('name')->get();
        
        return view('admin.subjects.edit', compact('subject', 'classes', 'existingSubjects'));
    }

    /**
     * Update the specified subject
     */
    public function update(Request $request, Subject $subject)
    {
        $request->validate([
            'subject_code' => 'required|string|max:20|unique:subjects,subject_code,' . $subject->id,
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category' => 'required|in:Core,Elective,Extra-curricular',
            'credits' => 'required|integer|min:1|max:10',
            'grade_levels' => 'nullable|array',
            'grade_levels.*' => 'string',
            'prerequisites' => 'nullable|array',
            'prerequisites.*' => 'exists:subjects,id',
            'is_active' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            $oldData = $subject->toArray();

            $subject->update([
                'subject_code' => $request->subject_code,
                'name' => $request->name,
                'description' => $request->description,
                'category' => $request->category,
                'credits' => $request->credits,
                'grade_levels' => $request->grade_levels,
                'prerequisites' => $request->prerequisites,
                'is_active' => $request->boolean('is_active', true),
            ]);

            // Log activity
            ActivityLog::log(
                'updated_subject',
                "Updated subject: {$subject->name} ({$subject->subject_code})",
                'App\Models\Subject',
                $subject->id,
                ['old_data' => $oldData, 'new_data' => $subject->fresh()->toArray()]
            );

            DB::commit();

            return redirect()->route('admin.academic.subjects.index')
                           ->with('success', 'Subject updated successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'Error updating subject: ' . $e->getMessage());
        }
    }



    /**
     * Remove the specified subject
     */
    public function destroy(Subject $subject)
    {
        try {
            DB::beginTransaction();

            // Check if subject is assigned to any classes or teachers using direct database queries
            $classesCount = \DB::table('class_subjects')->where('subject_id', $subject->id)->count();
            $teachersCount = \DB::table('teacher_subjects')->where('subject_id', $subject->id)->count();

            if ($classesCount > 0 || $teachersCount > 0) {
                return back()->with('error', "Cannot delete subject that is assigned to {$classesCount} classes and {$teachersCount} teachers.");
            }

            // Check if subject is a prerequisite for other subjects
            if ($subject->dependentSubjects()->count() > 0) {
                return back()->with('error', 'Cannot delete subject that is a prerequisite for other subjects.');
            }

            $subjectName = $subject->name;
            $subjectCode = $subject->subject_code;

            // Store subject data for undo
            $subjectData = [
                'id' => $subject->id,
                'subject_code' => $subject->subject_code,
                'name' => $subject->name,
                'description' => $subject->description,
                'category' => $subject->category,
                'credits' => $subject->credits,
                'grade_levels' => $subject->grade_levels,
                'prerequisites' => $subject->prerequisites,
                'is_active' => $subject->is_active,
            ];

            $subject->delete();

            // Log activity
            ActivityLog::log(
                'deleted_subject',
                "Deleted subject: {$subjectName} ({$subjectCode})",
                'App\Models\Subject',
                null
            );

            DB::commit();

            return redirect()->route('admin.academic.subjects.index')
                           ->with('success', "Subject \"{$subjectName}\" deleted successfully.")
                           ->with('undo_action', 'subject_delete')
                           ->with('undo_data', $subjectData)
                           ->with('undo_route', route('admin.academic.subjects.undo'));

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Error deleting subject: ' . $e->getMessage());
        }
    }

    /**
     * Toggle subject status
     */
    public function toggleStatus(Subject $subject)
    {
        try {
            $previousStatus = $subject->is_active;
            $subject->update(['is_active' => !$subject->is_active]);

            $status = $subject->is_active ? 'activated' : 'deactivated';

            // Log activity
            ActivityLog::log(
                $status . '_subject',
                "Subject {$status}: {$subject->name} ({$subject->subject_code})",
                'App\Models\Subject',
                $subject->id
            );

            return back()
                ->with('success', "Subject {$status} successfully.")
                ->with('undo_action', 'subject_toggle_status')
                ->with('undo_data', [
                    'id' => $subject->id,
                    'name' => $subject->name,
                    'status' => $previousStatus
                ])
                ->with('undo_route', route('admin.academic.subjects.undo'));

        } catch (\Exception $e) {
            return back()->with('error', 'Error updating subject status: ' . $e->getMessage());
        }
    }

    /**
     * Undo subject actions
     */
    public function undo(Request $request)
    {
        $action = $request->input('action');
        $data = $request->input('data');

        try {
            switch ($action) {
                case 'subject_delete':
                    $subject = Subject::create($data);

                    // Log activity
                    ActivityLog::log(
                        'restored_subject',
                        "Restored subject: {$subject->name} ({$subject->subject_code})",
                        'App\Models\Subject',
                        $subject->id
                    );

                    return response()->json([
                        'success' => true,
                        'message' => "Subject \"{$data['name']}\" has been restored."
                    ]);
                    break;

                case 'subject_toggle_status':
                    $subject = Subject::findOrFail($data['id']);
                    $subject->update(['is_active' => $data['status']]);
                    $status = $data['status'] ? 'activated' : 'deactivated';

                    // Log activity
                    ActivityLog::log(
                        $status . '_subject',
                        "Subject {$status}: {$subject->name} ({$subject->subject_code}) (undone)",
                        'App\Models\Subject',
                        $subject->id
                    );

                    return response()->json([
                        'success' => true,
                        'message' => "Subject \"{$data['name']}\" has been {$status} (undone)."
                    ]);
                    break;

                default:
                    return response()->json(['success' => false, 'message' => 'Unknown undo action.'], 400);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to undo action.'], 500);
        }
    }

    /**
     * Get classes for a subject (AJAX)
     */
    public function getClasses(Subject $subject)
    {
        try {
            \Log::info('Fetching classes for subject: ' . $subject->id);

            // Use direct database query to avoid relationship issues
            $classes = \DB::table('class_subjects')
                ->join('classes', 'classes.id', '=', 'class_subjects.class_id')
                ->where('class_subjects.subject_id', $subject->id)
                ->select('classes.*', 'class_subjects.is_mandatory', 'class_subjects.hours_per_week')
                ->orderBy('classes.name')
                ->get()
                ->map(function ($class) {
                    // Get counts manually using direct database queries
                    $sectionsCount = \DB::table('sections')
                        ->where('class_id', $class->id)
                        ->count();

                    $studentsCount = \DB::table('student_enrollments')
                        ->where('class_id', $class->id)
                        ->where('is_active', true)
                        ->count();

                    return [
                        'id' => $class->id,
                        'name' => $class->name ?? 'Unknown Class',
                        'level' => $class->level ?? 'Not specified',
                        'sections_count' => $sectionsCount,
                        'students_count' => $studentsCount,
                    ];
                });

            \Log::info('Found ' . $classes->count() . ' classes');

            return response()->json([
                'success' => true,
                'classes' => $classes
            ]);
        } catch (\Exception $e) {
            \Log::error('Error fetching classes for subject: ' . $e->getMessage());
            \Log::error('Stack trace: ' . $e->getTraceAsString());
            return response()->json([
                'success' => false,
                'message' => 'Error loading classes: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get teachers for a subject (AJAX)
     */
    public function getTeachers(Subject $subject)
    {
        try {
            \Log::info('Fetching teachers for subject: ' . $subject->id);

            $teachers = $subject->teachers()
                ->with('user')
                ->get()
                ->map(function ($teacher) {
                    // Get assigned classes count manually
                    $assignedClassesCount = \App\Models\TeacherSubject::where('teacher_id', $teacher->id)
                        ->distinct('class_id')
                        ->count('class_id');

                    return [
                        'id' => $teacher->id,
                        'name' => $teacher->user->name ?? 'Unknown Teacher',
                        'specialization' => $teacher->specialization ?? 'Not specified',
                        'classes_count' => $assignedClassesCount,
                        'is_primary' => $teacher->pivot->is_primary ?? false,
                    ];
                });

            \Log::info('Found ' . $teachers->count() . ' teachers');

            return response()->json([
                'success' => true,
                'teachers' => $teachers
            ]);
        } catch (\Exception $e) {
            \Log::error('Error fetching teachers for subject: ' . $e->getMessage());
            \Log::error('Stack trace: ' . $e->getTraceAsString());
            return response()->json([
                'success' => false,
                'message' => 'Error loading teachers: ' . $e->getMessage()
            ], 500);
        }
    }
}
