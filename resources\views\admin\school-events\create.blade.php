@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="Add School Event"
        description="Create a new school event"
        :back-route="route('admin.school-events.index')"
        back-label="Back to School Events">
    </x-page-header>

    <!-- Form -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Event Information</h3>
        </div>
        <form method="POST" action="{{ route('admin.school-events.store') }}" class="p-6">
            @csrf
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Title -->
                <div class="md:col-span-2">
                    <label for="title" class="block text-sm font-medium text-gray-700">Event Title *</label>
                    <input type="text" id="title" name="title" value="{{ old('title') }}" class="form-input mt-1" required placeholder="e.g., Annual Sports Day">
                    @error('title')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Event Date -->
                <div>
                    <label for="event_date" class="block text-sm font-medium text-gray-700">Event Date *</label>
                    <input type="date" id="event_date" name="event_date" value="{{ old('event_date') }}" class="form-input mt-1" required>
                    @error('event_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Location -->
                <div>
                    <label for="location" class="block text-sm font-medium text-gray-700">Location</label>
                    <input type="text" id="location" name="location" value="{{ old('location') }}" class="form-input mt-1" placeholder="e.g., School Auditorium">
                    @error('location')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Start Time -->
                <div>
                    <label for="start_time" class="block text-sm font-medium text-gray-700">Start Time</label>
                    <input type="time" id="start_time" name="start_time" value="{{ old('start_time') }}" class="form-input mt-1">
                    @error('start_time')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- End Time -->
                <div>
                    <label for="end_time" class="block text-sm font-medium text-gray-700">End Time</label>
                    <input type="time" id="end_time" name="end_time" value="{{ old('end_time') }}" class="form-input mt-1">
                    @error('end_time')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Type -->
                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700">Event Type *</label>
                    <select id="type" name="type" class="form-select mt-1" required>
                        <option value="">Select Type</option>
                        <option value="holiday" {{ old('type') == 'holiday' ? 'selected' : '' }}>Holiday</option>
                        <option value="exam" {{ old('type') == 'exam' ? 'selected' : '' }}>Exam</option>
                        <option value="sports" {{ old('type') == 'sports' ? 'selected' : '' }}>Sports</option>
                        <option value="cultural" {{ old('type') == 'cultural' ? 'selected' : '' }}>Cultural</option>
                        <option value="meeting" {{ old('type') == 'meeting' ? 'selected' : '' }}>Meeting</option>
                        <option value="other" {{ old('type') == 'other' ? 'selected' : '' }}>Other</option>
                    </select>
                    @error('type')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Scope -->
                <div>
                    <label for="scope" class="block text-sm font-medium text-gray-700">Scope *</label>
                    <select id="scope" name="scope" class="form-select mt-1" required onchange="toggleTargetClasses()">
                        <option value="">Select Scope</option>
                        <option value="all" {{ old('scope') == 'all' ? 'selected' : '' }}>All</option>
                        <option value="class" {{ old('scope') == 'class' ? 'selected' : '' }}>Specific Classes</option>
                        <option value="section" {{ old('scope') == 'section' ? 'selected' : '' }}>Specific Sections</option>
                        <option value="teachers" {{ old('scope') == 'teachers' ? 'selected' : '' }}>Teachers Only</option>
                        <option value="parents" {{ old('scope') == 'parents' ? 'selected' : '' }}>Parents Only</option>
                    </select>
                    @error('scope')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Target Classes (conditional) -->
            <div id="target-classes-section" class="mt-6" style="display: none;">
                <label class="block text-sm font-medium text-gray-700">Target Classes</label>
                <div class="mt-2 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                    @foreach($classes as $class)
                        <div class="flex items-center">
                            <input type="checkbox" id="class_{{ $class->id }}" name="target_classes[]" value="{{ $class->id }}" 
                                   {{ in_array($class->id, old('target_classes', [])) ? 'checked' : '' }} class="form-checkbox">
                            <label for="class_{{ $class->id }}" class="ml-2 text-sm text-gray-700">{{ $class->name }}</label>
                        </div>
                    @endforeach
                </div>
                @error('target_classes')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Description -->
            <div class="mt-6">
                <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                <textarea id="description" name="description" rows="4" class="form-textarea mt-1" placeholder="Event description and details...">{{ old('description') }}</textarea>
                @error('description')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Status -->
            <div class="mt-6">
                <div class="flex items-center">
                    <input type="checkbox" id="is_active" name="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }} class="form-checkbox">
                    <label for="is_active" class="ml-2 text-sm text-gray-700">Active</label>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="mt-8 flex items-center justify-end space-x-3">
                <a href="{{ route('admin.school-events.index') }}" class="btn-cancel">
                    Cancel
                </a>
                <button type="submit" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Create Event
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Toggle target classes section based on scope
function toggleTargetClasses() {
    const scope = document.getElementById('scope').value;
    const targetClassesSection = document.getElementById('target-classes-section');
    
    if (scope === 'class' || scope === 'section') {
        targetClassesSection.style.display = 'block';
    } else {
        targetClassesSection.style.display = 'none';
        // Uncheck all checkboxes when hiding
        const checkboxes = targetClassesSection.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => checkbox.checked = false);
    }
}

// Validate end time is after start time
document.getElementById('start_time').addEventListener('change', function() {
    const startTime = this.value;
    const endTimeInput = document.getElementById('end_time');
    
    if (startTime && endTimeInput.value && endTimeInput.value <= startTime) {
        alert('End time must be after start time');
        endTimeInput.value = '';
    }
});

document.getElementById('end_time').addEventListener('change', function() {
    const endTime = this.value;
    const startTime = document.getElementById('start_time').value;
    
    if (startTime && endTime && endTime <= startTime) {
        alert('End time must be after start time');
        this.value = '';
    }
});

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleTargetClasses();
});
</script>
@endpush
