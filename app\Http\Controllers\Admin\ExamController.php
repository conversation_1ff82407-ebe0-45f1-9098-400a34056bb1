<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Exam;
use App\Models\ExamSubject;
use App\Models\AcademicYear;
use App\Models\AcademicTerm;
use App\Models\GradeCategory;
use App\Models\GradeScale;
use App\Models\Subject;
use App\Models\ClassModel;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ExamController extends Controller
{
    /**
     * Display a listing of exams.
     */
    public function index()
    {
        $exams = Exam::with(['academicYear', 'academicTerm', 'gradeCategory', 'creator'])
                    ->orderBy('exam_date', 'desc')
                    ->get();

        $academicYears = AcademicYear::where('is_active', true)->get();
        $academicTerms = AcademicTerm::where('is_active', true)->get();
        $gradeCategories = GradeCategory::active()->get();

        $stats = [
            'total' => $exams->count(),
            'scheduled' => $exams->where('status', 'scheduled')->count(),
            'completed' => $exams->where('status', 'completed')->count(),
            'published' => $exams->where('is_published', true)->count(),
        ];

        return view('admin.exams.index', compact('exams', 'academicYears', 'academicTerms', 'gradeCategories', 'stats'));
    }

    /**
     * Show the form for creating a new exam.
     */
    public function create()
    {
        $academicYears = AcademicYear::where('is_active', true)->get();
        $academicTerms = AcademicTerm::where('is_active', true)->get();
        $gradeCategories = GradeCategory::active()->ordered()->get();
        $gradeScales = GradeScale::where('is_active', true)->get();
        $classes = ClassModel::where('is_active', true)->orderBy('sort_order')->get();
        $subjects = Subject::where('is_active', true)->orderBy('name')->get();
        $teachers = User::whereHas('teacher')->where('is_active', true)->get();

        return view('admin.exams.create', compact(
            'academicYears', 'academicTerms', 'gradeCategories', 'gradeScales', 
            'classes', 'subjects', 'teachers'
        ));
    }

    /**
     * Store a newly created exam.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'academic_year_id' => 'required|exists:academic_years,id',
            'academic_term_id' => 'required|exists:academic_terms,id',
            'grade_category_id' => 'required|exists:grade_categories,id',
            'grade_scale_id' => 'required|exists:grade_scales,id',
            'exam_date' => 'required|date|after_or_equal:today',
            'start_time' => 'nullable|date_format:H:i',
            'end_time' => 'nullable|date_format:H:i|after:start_time',
            'duration_minutes' => 'nullable|integer|min:1',
            'total_marks' => 'required|numeric|min:1',
            'passing_marks' => 'required|numeric|min:0|lte:total_marks',
            'instructions' => 'nullable|string',
            'target_classes' => 'required|array|min:1',
            'target_classes.*' => 'exists:classes,id',
            'subjects' => 'required|array|min:1',
            'subjects.*.subject_id' => 'required|exists:subjects,id',
            'subjects.*.teacher_id' => 'nullable|exists:users,id',
            'subjects.*.subject_total_marks' => 'required|numeric|min:1',
            'subjects.*.subject_passing_marks' => 'required|numeric|min:0',
            'subjects.*.subject_start_time' => 'nullable|date_format:H:i',
            'subjects.*.subject_end_time' => 'nullable|date_format:H:i',
            'subjects.*.subject_duration_minutes' => 'nullable|integer|min:1',
            'subjects.*.subject_instructions' => 'nullable|string',
        ]);

        DB::transaction(function () use ($request) {
            // Generate unique exam code
            $examCode = 'EX' . date('Y') . str_pad(Exam::count() + 1, 4, '0', STR_PAD_LEFT);

            $exam = Exam::create([
                'title' => $request->title,
                'exam_code' => $examCode,
                'description' => $request->description,
                'academic_year_id' => $request->academic_year_id,
                'academic_term_id' => $request->academic_term_id,
                'grade_category_id' => $request->grade_category_id,
                'grade_scale_id' => $request->grade_scale_id,
                'exam_date' => $request->exam_date,
                'start_time' => $request->start_time,
                'end_time' => $request->end_time,
                'duration_minutes' => $request->duration_minutes,
                'total_marks' => $request->total_marks,
                'passing_marks' => $request->passing_marks,
                'instructions' => $request->instructions,
                'target_classes' => $request->target_classes,
                'created_by' => auth()->id(),
                'status' => 'draft',
            ]);

            // Create exam subjects
            foreach ($request->subjects as $subjectData) {
                ExamSubject::create([
                    'exam_id' => $exam->id,
                    'subject_id' => $subjectData['subject_id'],
                    'teacher_id' => $subjectData['teacher_id'],
                    'subject_total_marks' => $subjectData['subject_total_marks'],
                    'subject_passing_marks' => $subjectData['subject_passing_marks'],
                    'subject_start_time' => $subjectData['subject_start_time'],
                    'subject_end_time' => $subjectData['subject_end_time'],
                    'subject_duration_minutes' => $subjectData['subject_duration_minutes'],
                    'subject_instructions' => $subjectData['subject_instructions'],
                ]);
            }
        });

        return redirect()->route('admin.exams.index')
                        ->with('success', 'Exam created successfully.');
    }

    /**
     * Display the specified exam.
     */
    public function show(Exam $exam)
    {
        $exam->load([
            'academicYear', 'academicTerm', 'gradeCategory', 'gradeScale', 'creator',
            'examSubjects.subject', 'examSubjects.teacher', 'grades.student.user'
        ]);

        $targetClasses = ClassModel::whereIn('id', $exam->target_classes ?? [])->get();

        return view('admin.exams.show', compact('exam', 'targetClasses'));
    }

    /**
     * Show the form for editing the specified exam.
     */
    public function edit(Exam $exam)
    {
        $exam->load('examSubjects');
        
        $academicYears = AcademicYear::where('is_active', true)->get();
        $academicTerms = AcademicTerm::where('is_active', true)->get();
        $gradeCategories = GradeCategory::active()->ordered()->get();
        $gradeScales = GradeScale::where('is_active', true)->get();
        $classes = ClassModel::where('is_active', true)->orderBy('sort_order')->get();
        $subjects = Subject::where('is_active', true)->orderBy('name')->get();
        $teachers = User::whereHas('teacher')->where('is_active', true)->get();

        return view('admin.exams.edit', compact(
            'exam', 'academicYears', 'academicTerms', 'gradeCategories', 
            'gradeScales', 'classes', 'subjects', 'teachers'
        ));
    }

    /**
     * Update the specified exam.
     */
    public function update(Request $request, Exam $exam)
    {
        // Prevent editing if exam is completed or has grades
        if ($exam->status === 'completed' || $exam->grades()->exists()) {
            return back()->withErrors(['error' => 'Cannot edit exam that is completed or has grades.']);
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'exam_code' => 'required|string|max:50|unique:exams,exam_code,' . $exam->id,
            'description' => 'nullable|string',
            'academic_year_id' => 'required|exists:academic_years,id',
            'academic_term_id' => 'required|exists:academic_terms,id',
            'grade_category_id' => 'required|exists:grade_categories,id',
            'grade_scale_id' => 'required|exists:grade_scales,id',
            'exam_date' => 'required|date',
            'start_time' => 'nullable|date_format:H:i',
            'end_time' => 'nullable|date_format:H:i|after:start_time',
            'duration_minutes' => 'nullable|integer|min:1',
            'total_marks' => 'required|numeric|min:1',
            'passing_marks' => 'required|numeric|min:0|lte:total_marks',
            'instructions' => 'nullable|string',
            'target_classes' => 'required|array|min:1',
            'target_classes.*' => 'exists:classes,id',
            'subjects' => 'required|array|min:1',
            'subjects.*.subject_id' => 'required|exists:subjects,id',
            'subjects.*.teacher_id' => 'nullable|exists:users,id',
            'subjects.*.subject_total_marks' => 'required|numeric|min:1',
            'subjects.*.subject_passing_marks' => 'required|numeric|min:0',
            'subjects.*.subject_start_time' => 'nullable|date_format:H:i',
            'subjects.*.subject_end_time' => 'nullable|date_format:H:i',
            'subjects.*.subject_duration_minutes' => 'nullable|integer|min:1',
            'subjects.*.subject_instructions' => 'nullable|string',
        ]);

        DB::transaction(function () use ($request, $exam) {
            // Update exam basic information
            $exam->update($request->only([
                'title', 'exam_code', 'description', 'academic_year_id', 'academic_term_id',
                'grade_category_id', 'grade_scale_id', 'exam_date', 'start_time',
                'end_time', 'duration_minutes', 'total_marks', 'passing_marks',
                'instructions', 'target_classes'
            ]));

            // Delete existing exam subjects
            $exam->examSubjects()->delete();

            // Create new exam subjects
            foreach ($request->subjects as $subjectData) {
                ExamSubject::create([
                    'exam_id' => $exam->id,
                    'subject_id' => $subjectData['subject_id'],
                    'teacher_id' => $subjectData['teacher_id'],
                    'subject_total_marks' => $subjectData['subject_total_marks'],
                    'subject_passing_marks' => $subjectData['subject_passing_marks'],
                    'subject_start_time' => $subjectData['subject_start_time'],
                    'subject_end_time' => $subjectData['subject_end_time'],
                    'subject_duration_minutes' => $subjectData['subject_duration_minutes'],
                    'subject_instructions' => $subjectData['subject_instructions'],
                ]);
            }
        });

        return redirect()->route('admin.grading.exams.show', $exam)
                        ->with('success', 'Exam updated successfully.');
    }

    /**
     * Remove the specified exam.
     */
    public function destroy(Exam $exam)
    {
        // Prevent deletion if exam has grades
        if ($exam->grades()->exists()) {
            return back()->withErrors(['error' => 'Cannot delete exam that has grades.']);
        }

        $exam->delete();

        return redirect()->route('admin.exams.index')
                        ->with('success', 'Exam deleted successfully.');
    }

    /**
     * Publish or unpublish an exam.
     */
    public function publish(Exam $exam)
    {
        $exam->update(['is_published' => !$exam->is_published]);

        $status = $exam->is_published ? 'published' : 'unpublished';
        return back()->with('success', "Exam {$status} successfully.");
    }

    /**
     * Schedule an exam.
     */
    public function schedule(Exam $exam)
    {
        $exam->update(['status' => 'scheduled']);

        return back()->with('success', 'Exam scheduled successfully.');
    }
}
