<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Student Grades - Store all grades for students
        Schema::create('grades', function (Blueprint $table) {
            $table->id();
            $table->foreignId('student_id')->constrained()->onDelete('cascade');
            $table->foreignId('academic_year_id')->constrained()->onDelete('cascade');
            $table->foreignId('academic_term_id')->constrained()->onDelete('cascade');
            $table->foreignId('subject_id')->constrained()->onDelete('cascade');
            $table->foreignId('grade_category_id')->constrained()->onDelete('cascade');
            $table->foreignId('grade_scale_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('teacher_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('recorded_by')->constrained('users')->onDelete('cascade');

            // Grade type and specific references
            $table->enum('grade_type', ['exam', 'assignment', 'quiz', 'project', 'participation'])->default('exam');
            $table->foreignId('exam_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('assignment_id')->nullable()->constrained()->onDelete('cascade');

            // Polymorphic relationship - can be linked to exam, assignment, or manual entry
            $table->string('gradeable_type')->nullable(); // App\Models\Exam, App\Models\Assignment, etc.
            $table->unsignedBigInteger('gradeable_id')->nullable();

            $table->decimal('marks_obtained', 8, 2);
            $table->decimal('total_marks', 8, 2);
            $table->decimal('percentage', 5, 2); // Calculated percentage
            $table->string('grade_letter', 5)->nullable(); // A, B+, C, etc.
            $table->string('letter_grade', 5)->nullable(); // A, B+, C, etc. (for backward compatibility)
            $table->decimal('grade_point', 3, 2)->nullable(); // GPA points
            $table->decimal('grade_points', 3, 2)->nullable(); // GPA points (for backward compatibility)
            $table->boolean('is_passed')->default(false);
            $table->boolean('is_published')->default(false);

            $table->text('teacher_comments')->nullable();
            $table->text('feedback')->nullable();
            $table->text('remarks')->nullable();

            $table->enum('status', ['draft', 'published', 'revised'])->default('draft');
            $table->timestamp('graded_at')->nullable();
            $table->timestamp('published_at')->nullable();

            $table->timestamps();

            // Indexes for performance
            $table->index(['gradeable_type', 'gradeable_id']);
            $table->index(['student_id', 'academic_year_id', 'academic_term_id']);
            $table->index(['subject_id', 'grade_category_id']);
            $table->index(['grade_type']);
            $table->index(['exam_id']);
            $table->index(['assignment_id']);
        });

        // Grade Reports - Store compiled report cards/transcripts
        Schema::create('grade_reports', function (Blueprint $table) {
            $table->id();
            $table->foreignId('student_id')->constrained()->onDelete('cascade');
            $table->foreignId('academic_year_id')->constrained()->onDelete('cascade');
            $table->foreignId('academic_term_id')->constrained()->onDelete('cascade');
            $table->string('report_type'); // term_report, annual_report, transcript
            
            $table->json('subject_grades'); // Compiled grades by subject
            $table->decimal('overall_percentage', 5, 2)->nullable();
            $table->decimal('overall_gpa', 3, 2)->nullable();
            $table->string('overall_grade', 5)->nullable();
            $table->integer('class_rank')->nullable();
            $table->integer('total_students')->nullable();
            
            $table->text('teacher_comments')->nullable();
            $table->text('principal_comments')->nullable();
            $table->text('parent_comments')->nullable();
            
            $table->integer('attendance_days_present')->nullable();
            $table->integer('attendance_total_days')->nullable();
            $table->decimal('attendance_percentage', 5, 2)->nullable();
            
            $table->enum('status', ['draft', 'published', 'sent'])->default('draft');
            $table->timestamp('published_at')->nullable();
            $table->timestamp('sent_at')->nullable();
            
            $table->foreignId('generated_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();

            // Ensure one report per student per term
            $table->unique(['student_id', 'academic_year_id', 'academic_term_id', 'report_type']);
        });

        // Grade Comments Templates - Predefined comments for teachers
        Schema::create('grade_comment_templates', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('comment');
            $table->string('category'); // positive, improvement, concern, excellent
            $table->json('grade_range')->nullable(); // Which grade ranges this applies to
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->boolean('is_public')->default(false); // Available to all teachers
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('grade_comment_templates');
        Schema::dropIfExists('grade_reports');
        Schema::dropIfExists('grades');
    }
};
