<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ClassModel;
use App\Models\Subject;
use App\Models\Section;
use Illuminate\Support\Facades\DB;

class ClassSubjectRelationshipsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Creating class-subject relationships and sections...');

        // First, create sections for classes that don't have them
        $this->createSections();
        
        // Then create class-subject relationships
        $this->createClassSubjectRelationships();
    }

    private function createSections()
    {
        $this->command->info('Creating sections for classes...');
        
        $classes = ClassModel::all();
        
        foreach ($classes as $class) {
            // Check if class already has sections
            if ($class->sections()->count() == 0) {
                // Create 2-3 sections per class
                $sectionNames = ['A', 'B', 'C'];
                $numSections = rand(2, 3);
                
                for ($i = 0; $i < $numSections; $i++) {
                    Section::create([
                        'class_id' => $class->id,
                        'name' => $sectionNames[$i],
                        'capacity' => rand(25, 35),
                        'is_active' => true,
                    ]);
                }
            }
        }
    }

    private function createClassSubjectRelationships()
    {
        $this->command->info('Assigning subjects to classes...');
        
        $classes = ClassModel::all();
        $subjects = Subject::all();
        
        // Define core subjects that should be mandatory for most classes
        $coreSubjects = [
            'Mathematics',
            'English Language',
            'Science',
            'Bahasa Malaysia',
            'History',
            'Geography'
        ];

        // Define elective subjects
        $electiveSubjects = [
            'Physics',
            'Chemistry',
            'Biology',
            'Information Technology',
            'Visual Arts',
            'Music',
            'Physical Education',
            'Drama Club',
            'Debate Club',
            'Science Club'
        ];

        foreach ($classes as $class) {
            // Assign core subjects as mandatory
            foreach ($coreSubjects as $subjectName) {
                $subject = $subjects->where('name', $subjectName)->first();
                if ($subject) {
                    DB::table('class_subjects')->updateOrInsert([
                        'class_id' => $class->id,
                        'subject_id' => $subject->id,
                    ], [
                        'is_mandatory' => true,
                        'hours_per_week' => rand(3, 5),
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                    $this->command->info("Assigned {$subjectName} to {$class->name} (mandatory)");
                }
            }

            // Assign 3-5 elective subjects
            $numElectives = rand(3, 5);
            $selectedElectives = collect($electiveSubjects)->random($numElectives);

            foreach ($selectedElectives as $subjectName) {
                $subject = $subjects->where('name', $subjectName)->first();
                if ($subject) {
                    DB::table('class_subjects')->updateOrInsert([
                        'class_id' => $class->id,
                        'subject_id' => $subject->id,
                    ], [
                        'is_mandatory' => false,
                        'hours_per_week' => rand(2, 4),
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                    $this->command->info("Assigned {$subjectName} to {$class->name} (elective)");
                }
            }
        }
        
        $this->command->info('Class-subject relationships created successfully!');
        $this->command->info('- Total relationships: ' . DB::table('class_subjects')->count());
        $this->command->info('- Mandatory subjects: ' . DB::table('class_subjects')->where('is_mandatory', true)->count());
        $this->command->info('- Elective subjects: ' . DB::table('class_subjects')->where('is_mandatory', false)->count());
    }
}
