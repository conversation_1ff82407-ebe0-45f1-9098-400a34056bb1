@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <x-page-header
        title="Assignment Submissions"
        description="View and manage submissions for {{ $assignment->title }}"
        :back-route="route('admin.grading.assignments.show', $assignment)"
        back-label="Back to Assignment" />

    <!-- Assignment Info -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div>
                <label class="block text-sm font-medium text-gray-700">Assignment</label>
                <p class="mt-1 text-sm text-gray-900">{{ $assignment->title }}</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Subject</label>
                <p class="mt-1 text-sm text-gray-900">{{ $assignment->subject->name }}</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Class</label>
                <p class="mt-1 text-sm text-gray-900">{{ $assignment->classRoom->name }}</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Due Date</label>
                <p class="mt-1 text-sm text-gray-900">{{ $assignment->due_date->format('M d, Y') }}</p>
            </div>
        </div>
    </div>

    <!-- Submissions List -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Submissions ({{ $submissions->total() }})</h3>
        </div>

        @if($submissions->count() > 0)
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Submitted At</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Marks</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($submissions as $submission)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                            <span class="text-sm font-medium text-gray-700">
                                                {{ substr($submission->student->user->name, 0, 2) }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">{{ $submission->student->user->name }}</div>
                                        <div class="text-sm text-gray-500">{{ $submission->student->student_id }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ $submission->submitted_at ? $submission->submitted_at->format('M d, Y H:i') : 'Not submitted' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($submission->grade && $submission->grade->marks_obtained !== null)
                                    <span class="badge badge-green">Graded</span>
                                @elseif($submission->submitted_at)
                                    <span class="badge badge-yellow">Pending Review</span>
                                @else
                                    <span class="badge badge-gray">Not Submitted</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                @if($submission->grade && $submission->grade->marks_obtained !== null)
                                    {{ number_format($submission->grade->marks_obtained, 1) }}/{{ number_format($assignment->total_marks, 1) }}
                                @else
                                    -
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                @if($submission->submitted_at)
                                    <a href="#" class="text-indigo-600 hover:text-indigo-900" style="cursor: pointer;">View</a>
                                    @if(!$submission->grade || $submission->grade->marks_obtained === null)
                                        <span class="text-gray-300 mx-1">|</span>
                                        <a href="#" class="text-green-600 hover:text-green-900" style="cursor: pointer;">Grade</a>
                                    @endif
                                @else
                                    <span class="text-gray-400">No submission</span>
                                @endif
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="px-6 py-4 border-t border-gray-200">
                {{ $submissions->links() }}
            </div>
        @else
            <div class="px-6 py-8 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No submissions</h3>
                <p class="mt-1 text-sm text-gray-500">No students have submitted this assignment yet.</p>
            </div>
        @endif
    </div>
</div>
@endsection
