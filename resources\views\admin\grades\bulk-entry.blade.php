@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <x-page-header
        title="Bulk Grade Entry"
        description="Enter grades for multiple students at once"
        :back-route="route('admin.grading.grades.index')"
        back-label="Back to Grades">
    </x-page-header>

    <!-- Bulk Entry Form -->
    <div class="card">
        <form method="POST" action="{{ route('admin.grading.grades.bulk-store') }}" class="space-y-6">
            @csrf

            <!-- Common Settings -->
            <div class="border-b border-gray-200 pb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Assessment Details</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Academic Year -->
                    <div>
                        <label for="academic_year_id" class="block text-sm font-medium text-gray-700 mb-1">Academic Year *</label>
                        <select name="academic_year_id" id="academic_year_id" class="form-select" required>
                            <option value="">Select Academic Year</option>
                            @foreach($academicYears as $year)
                                <option value="{{ $year->id }}" {{ old('academic_year_id') == $year->id ? 'selected' : '' }}>
                                    {{ $year->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('academic_year_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Academic Term -->
                    <div>
                        <label for="academic_term_id" class="block text-sm font-medium text-gray-700 mb-1">Academic Term *</label>
                        <select name="academic_term_id" id="academic_term_id" class="form-select" required>
                            <option value="">Select Academic Term</option>
                            @foreach($academicTerms as $term)
                                <option value="{{ $term->id }}" {{ old('academic_term_id') == $term->id ? 'selected' : '' }}>
                                    {{ $term->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('academic_term_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Subject -->
                    <div>
                        <label for="subject_id" class="block text-sm font-medium text-gray-700 mb-1">Subject *</label>
                        <select name="subject_id" id="subject_id" class="form-select" required>
                            <option value="">Select Subject</option>
                            @foreach($subjects as $subject)
                                <option value="{{ $subject->id }}" {{ old('subject_id') == $subject->id ? 'selected' : '' }}>
                                    {{ $subject->name }} ({{ $subject->code }})
                                </option>
                            @endforeach
                        </select>
                        @error('subject_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Grade Category -->
                    <div>
                        <label for="grade_category_id" class="block text-sm font-medium text-gray-700 mb-1">Grade Category *</label>
                        <select name="grade_category_id" id="grade_category_id" class="form-select" required>
                            <option value="">Select Category</option>
                            @foreach($gradeCategories as $category)
                                <option value="{{ $category->id }}" {{ old('grade_category_id') == $category->id ? 'selected' : '' }}>
                                    {{ $category->name }} ({{ $category->weight }}%)
                                </option>
                            @endforeach
                        </select>
                        @error('grade_category_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Grade Type -->
                    <div>
                        <label for="grade_type" class="block text-sm font-medium text-gray-700 mb-1">Grade Type *</label>
                        <select name="grade_type" id="grade_type" class="form-select" required>
                            <option value="">Select Type</option>
                            <option value="exam" {{ old('grade_type') == 'exam' ? 'selected' : '' }}>Exam</option>
                            <option value="assignment" {{ old('grade_type') == 'assignment' ? 'selected' : '' }}>Assignment</option>
                            <option value="quiz" {{ old('grade_type') == 'quiz' ? 'selected' : '' }}>Quiz</option>
                            <option value="project" {{ old('grade_type') == 'project' ? 'selected' : '' }}>Project</option>
                            <option value="participation" {{ old('grade_type') == 'participation' ? 'selected' : '' }}>Participation</option>
                        </select>
                        @error('grade_type')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Total Marks -->
                    <div>
                        <label for="total_marks" class="block text-sm font-medium text-gray-700 mb-1">Total Marks *</label>
                        <input type="number" name="total_marks" id="total_marks" 
                               class="form-input" step="0.1" min="0" 
                               value="{{ old('total_marks') }}" required>
                        @error('total_marks')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                    <!-- Exam (conditional) -->
                    <div id="exam_field" style="display: none;">
                        <label for="exam_id" class="block text-sm font-medium text-gray-700 mb-1">Exam</label>
                        <select name="exam_id" id="exam_id" class="form-select">
                            <option value="">Select Exam</option>
                            @foreach($exams as $exam)
                                <option value="{{ $exam->id }}" {{ old('exam_id') == $exam->id ? 'selected' : '' }}>
                                    {{ $exam->title }}
                                </option>
                            @endforeach
                        </select>
                        @error('exam_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Assignment (conditional) -->
                    <div id="assignment_field" style="display: none;">
                        <label for="assignment_id" class="block text-sm font-medium text-gray-700 mb-1">Assignment</label>
                        <select name="assignment_id" id="assignment_id" class="form-select">
                            <option value="">Select Assignment</option>
                            @foreach($assignments as $assignment)
                                <option value="{{ $assignment->id }}" {{ old('assignment_id') == $assignment->id ? 'selected' : '' }}>
                                    {{ $assignment->title }}
                                </option>
                            @endforeach
                        </select>
                        @error('assignment_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Class Selection -->
                <div class="mt-6">
                    <label for="class_room_id" class="block text-sm font-medium text-gray-700 mb-1">Class *</label>
                    <select name="class_room_id" id="class_room_id" class="form-select" required>
                        <option value="">Select Class to Load Students</option>
                        @foreach($classRooms as $class)
                            <option value="{{ $class->id }}">{{ $class->name }}</option>
                        @endforeach
                    </select>
                    <p class="mt-1 text-sm text-gray-500">Select a class to load students for grade entry</p>
                </div>
            </div>

            <!-- Students Grade Entry -->
            <div id="students_section" style="display: none;">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Student Grades</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Marks Obtained *</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Percentage</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
                            </tr>
                        </thead>
                        <tbody id="students_tbody" class="bg-white divide-y divide-gray-200">
                            <!-- Students will be loaded here via JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="{{ route('admin.grading.grades.index') }}" class="btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn-primary" id="submit_btn" style="display: none;">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Save All Grades
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const gradeTypeSelect = document.getElementById('grade_type');
    const examField = document.getElementById('exam_field');
    const assignmentField = document.getElementById('assignment_field');
    const classSelect = document.getElementById('class_room_id');
    const studentsSection = document.getElementById('students_section');
    const studentsTbody = document.getElementById('students_tbody');
    const submitBtn = document.getElementById('submit_btn');
    const totalMarksInput = document.getElementById('total_marks');

    function toggleFields() {
        const gradeType = gradeTypeSelect.value;
        
        examField.style.display = gradeType === 'exam' ? 'block' : 'none';
        assignmentField.style.display = gradeType === 'assignment' ? 'block' : 'none';
    }

    function calculatePercentage(marksInput) {
        const marksObtained = parseFloat(marksInput.value) || 0;
        const totalMarks = parseFloat(totalMarksInput.value) || 0;
        const percentage = totalMarks > 0 ? (marksObtained / totalMarks * 100).toFixed(1) : 0;
        
        const percentageCell = marksInput.closest('tr').querySelector('.percentage-cell');
        percentageCell.textContent = percentage + '%';
    }

    function loadStudents() {
        const classId = classSelect.value;
        if (!classId) {
            studentsSection.style.display = 'none';
            submitBtn.style.display = 'none';
            return;
        }

        // This would typically be an AJAX call to load students
        // For now, we'll show a placeholder
        studentsTbody.innerHTML = `
            <tr>
                <td colspan="4" class="px-6 py-4 text-center text-gray-500">
                    Loading students... (This would be implemented with AJAX)
                </td>
            </tr>
        `;
        
        studentsSection.style.display = 'block';
        submitBtn.style.display = 'inline-flex';
    }

    gradeTypeSelect.addEventListener('change', toggleFields);
    classSelect.addEventListener('change', loadStudents);
    totalMarksInput.addEventListener('input', function() {
        // Recalculate all percentages when total marks change
        document.querySelectorAll('.marks-input').forEach(calculatePercentage);
    });

    toggleFields(); // Initial call
});
</script>
@endsection
