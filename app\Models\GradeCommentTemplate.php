<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class GradeCommentTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'comment',
        'category',
        'grade_range',
        'created_by',
        'is_public',
        'is_active',
    ];

    protected $casts = [
        'grade_range' => 'array',
        'is_public' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get the creator.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope for active templates.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for public templates.
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope for templates by category.
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Get category badge color.
     */
    public function getCategoryBadgeColorAttribute(): string
    {
        return match($this->category) {
            'excellent' => 'badge-green',
            'positive' => 'badge-blue',
            'improvement' => 'badge-yellow',
            'concern' => 'badge-red',
            default => 'badge-gray',
        };
    }

    /**
     * Check if template applies to a given percentage.
     */
    public function appliesTo(float $percentage): bool
    {
        if (!$this->grade_range) {
            return true; // No range specified, applies to all
        }

        $min = $this->grade_range['min'] ?? 0;
        $max = $this->grade_range['max'] ?? 100;

        return $percentage >= $min && $percentage <= $max;
    }
}
