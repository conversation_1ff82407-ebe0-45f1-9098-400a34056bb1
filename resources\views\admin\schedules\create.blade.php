@extends('layouts.app')

@section('title', 'Create Schedule')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="Create Schedule"
        description="Add a new class schedule"
        :back-route="route('admin.schedules.index')"
        back-label="Back to Schedules">
    </x-page-header>

    <!-- Create Form -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Schedule Information</h3>
        </div>
        
        <form action="{{ route('admin.schedules.store') }}" method="POST" class="p-6">
            @csrf
            
            @if($errors->any())
                <div class="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">Please correct the following errors:</h3>
                            <div class="mt-2 text-sm text-red-700">
                                <ul class="list-disc pl-5 space-y-1">
                                    @foreach($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Academic Information -->
                <div class="space-y-4">
                    <div>
                        <label for="academic_year_id" class="block text-sm font-medium text-gray-700">Academic Year</label>
                        <select name="academic_year_id" id="academic_year_id" class="form-select" required>
                            <option value="">Select Academic Year</option>
                            @foreach($academicYears as $year)
                                <option value="{{ $year->id }}" {{ old('academic_year_id') == $year->id ? 'selected' : '' }}>
                                    {{ $year->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('academic_year_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="academic_term_id" class="block text-sm font-medium text-gray-700">Academic Term</label>
                        <select name="academic_term_id" id="academic_term_id" class="form-select" required>
                            <option value="">Select Academic Term</option>
                            @foreach($academicTerms as $term)
                                <option value="{{ $term->id }}" {{ old('academic_term_id') == $term->id ? 'selected' : '' }}>
                                    {{ $term->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('academic_term_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="class_id" class="block text-sm font-medium text-gray-700">Class</label>
                        <select name="class_id" id="class_id" class="form-select" required>
                            <option value="">Select Class</option>
                            @foreach($classes as $class)
                                <option value="{{ $class->id }}" {{ old('class_id') == $class->id ? 'selected' : '' }}>
                                    {{ $class->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('class_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="section_id" class="block text-sm font-medium text-gray-700">Section</label>
                        <select name="section_id" id="section_id" class="form-select" required>
                            <option value="">Select Section</option>
                            @foreach($sections as $section)
                                <option value="{{ $section->id }}" {{ old('section_id') == $section->id ? 'selected' : '' }}>
                                    {{ $section->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('section_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Schedule Details -->
                <div class="space-y-4">
                    <div>
                        <label for="subject_id" class="block text-sm font-medium text-gray-700">Subject</label>
                        <select name="subject_id" id="subject_id" class="form-select" required>
                            <option value="">Select Subject</option>
                            @foreach($subjects as $subject)
                                <option value="{{ $subject->id }}" {{ old('subject_id') == $subject->id ? 'selected' : '' }}>
                                    {{ $subject->name }} ({{ $subject->code }})
                                </option>
                            @endforeach
                        </select>
                        @error('subject_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Teacher</label>

                        @if($teachers->count() > 0)
                            <!-- Single Select Search Component -->
                            <div x-data="singleSelectSearch()" class="relative"
                                 x-init="
                                    items = [
                                        @foreach($teachers as $teacher)
                                        {
                                            id: {{ $teacher->id }},
                                            name: '{{ addslashes($teacher->name ?? '') }}',
                                            subtitle: '{{ addslashes(($teacher->email ?? '') . ' • Teacher') }}',
                                            searchText: '{{ addslashes(($teacher->name ?? '') . ' ' . ($teacher->email ?? '')) }}'
                                        },
                                        @endforeach
                                    ];
                                    selected = {{ old('teacher_id', 'null') }};
                                    name = 'teacher_id';
                                    placeholder = 'Search and select a teacher...';
                                    init();
                                 ">

                                <!-- Search Input -->
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                        </svg>
                                    </div>
                                    <input
                                        type="text"
                                        x-model="searchQuery"
                                        @focus="showDropdown = true"
                                        @click.away="showDropdown = false"
                                        class="search-input @error('teacher_id') border-red-500 @enderror"
                                        :placeholder="selectedItem ? selectedItem.name : placeholder"
                                        autocomplete="off"
                                        required
                                    >
                                </div>

                                <!-- Selected Item Display -->
                                <div x-show="selectedItem" class="mt-2">
                                    <div class="inline-flex items-center px-3 py-1 rounded-md text-sm bg-blue-100 text-blue-800">
                                        <span x-text="selectedItem ? selectedItem.name : ''"></span>
                                        <button type="button" @click="clearSelection()" class="ml-2 text-blue-600 hover:text-blue-800">
                                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>

                                <!-- Dropdown -->
                                <div x-show="showDropdown && filteredItems.length > 0"
                                     x-transition:enter="transition ease-out duration-100"
                                     x-transition:enter-start="transform opacity-0 scale-95"
                                     x-transition:enter-end="transform opacity-100 scale-100"
                                     x-transition:leave="transition ease-in duration-75"
                                     x-transition:leave-start="transform opacity-100 scale-100"
                                     x-transition:leave-end="transform opacity-0 scale-95"
                                     class="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base border border-gray-200 overflow-auto focus:outline-none sm:text-sm">
                                    <template x-for="item in filteredItems" :key="item.id">
                                        <div @click="selectItem(item)"
                                             class="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-blue-50">
                                            <div class="flex items-center">
                                                <span class="font-medium block truncate" x-text="item.name"></span>
                                            </div>
                                            <span class="text-gray-500 text-sm block truncate" x-text="item.subtitle"></span>
                                        </div>
                                    </template>
                                </div>

                                <!-- Hidden input for form submission -->
                                <input type="hidden" :name="name" :value="selectedItem ? selectedItem.id : ''" required>
                            </div>
                        @else
                            <div class="text-center py-4">
                                <p class="text-sm text-gray-500">No teachers available.</p>
                            </div>
                        @endif

                        @error('teacher_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="day_of_week" class="block text-sm font-medium text-gray-700">Day of Week</label>
                        <select name="day_of_week" id="day_of_week" class="form-select" required>
                            <option value="">Select Day</option>
                            @foreach($weekDays as $value => $label)
                                <option value="{{ $value }}" {{ old('day_of_week') == $value ? 'selected' : '' }}>
                                    {{ $label }}
                                </option>
                            @endforeach
                        </select>
                        @error('day_of_week')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="time_slot_id" class="block text-sm font-medium text-gray-700">Time Slot</label>
                        <select name="time_slot_id" id="time_slot_id" class="form-select" required>
                            <option value="">Select Time Slot</option>
                            @foreach($timeSlots as $timeSlot)
                                <option value="{{ $timeSlot->id }}" {{ old('time_slot_id') == $timeSlot->id ? 'selected' : '' }}>
                                    {{ \Carbon\Carbon::parse($timeSlot->start_time)->format('g:i A') }} - 
                                    {{ \Carbon\Carbon::parse($timeSlot->end_time)->format('g:i A') }}
                                </option>
                            @endforeach
                        </select>
                        @error('time_slot_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Room Number -->
            <div class="mt-6">
                <label for="room_number" class="block text-sm font-medium text-gray-700">Room Number (Optional)</label>
                <input type="text" name="room_number" id="room_number" value="{{ old('room_number') }}" 
                       class="form-input" placeholder="Enter room number">
                @error('room_number')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Form Actions -->
            <div class="mt-8 flex items-center justify-end space-x-3">
                <a href="{{ route('admin.schedules.index') }}" class="btn-cancel">
                    Cancel
                </a>
                <button type="submit" 
                        class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Create Schedule
                </button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
// Single Select Search Component
function singleSelectSearch() {
    return {
        items: [],
        selectedItem: null,
        searchQuery: '',
        showDropdown: false,
        name: 'item',
        placeholder: 'Search and select an item...',
        selected: null,

        init() {
            // Initialize selected item based on this.selected
            if (this.selected) {
                this.selectedItem = this.items.find(item => item.id == this.selected);
                if (this.selectedItem) {
                    this.searchQuery = this.selectedItem.name;
                }
            }
        },

        get filteredItems() {
            if (!this.searchQuery) {
                return this.items;
            }

            return this.items.filter(item => {
                return item.searchText.toLowerCase().includes(this.searchQuery.toLowerCase());
            });
        },

        selectItem(item) {
            this.selectedItem = item;
            this.searchQuery = item.name;
            this.showDropdown = false;
        },

        clearSelection() {
            this.selectedItem = null;
            this.searchQuery = '';
            this.showDropdown = false;
        }
    };
}
</script>
@endpush
@endsection
