<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Grade extends Model
{
    use HasFactory;

    protected $fillable = [
        'student_id',
        'academic_year_id',
        'academic_term_id',
        'subject_id',
        'grade_category_id',
        'grade_scale_id',
        'teacher_id',
        'grade_type',
        'exam_id',
        'assignment_id',
        'gradeable_type',
        'gradeable_id',
        'marks_obtained',
        'total_marks',
        'percentage',
        'grade_letter',
        'grade_point',
        'grade_points',
        'letter_grade',
        'teacher_comments',
        'feedback',
        'remarks',
        'status',
        'is_passed',
        'is_published',
        'recorded_by',
        'graded_at',
        'published_at',
    ];

    protected $casts = [
        'marks_obtained' => 'decimal:2',
        'total_marks' => 'decimal:2',
        'percentage' => 'decimal:2',
        'grade_point' => 'decimal:2',
        'grade_points' => 'decimal:2',
        'is_passed' => 'boolean',
        'is_published' => 'boolean',
        'graded_at' => 'datetime',
        'published_at' => 'datetime',
    ];

    /**
     * Get the student.
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * Get the academic year.
     */
    public function academicYear(): BelongsTo
    {
        return $this->belongsTo(AcademicYear::class);
    }

    /**
     * Get the academic term.
     */
    public function academicTerm(): BelongsTo
    {
        return $this->belongsTo(AcademicTerm::class);
    }

    /**
     * Get the subject.
     */
    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class);
    }

    /**
     * Get the grade category.
     */
    public function gradeCategory(): BelongsTo
    {
        return $this->belongsTo(GradeCategory::class);
    }

    /**
     * Get the grade scale.
     */
    public function gradeScale(): BelongsTo
    {
        return $this->belongsTo(GradeScale::class);
    }

    /**
     * Get the teacher.
     */
    public function teacher(): BelongsTo
    {
        return $this->belongsTo(User::class, 'teacher_id');
    }

    /**
     * Get the recorded by user.
     */
    public function recordedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'recorded_by');
    }

    /**
     * Get the exam.
     */
    public function exam(): BelongsTo
    {
        return $this->belongsTo(Exam::class);
    }

    /**
     * Get the assignment.
     */
    public function assignment(): BelongsTo
    {
        return $this->belongsTo(Assignment::class);
    }

    /**
     * Get the gradeable item (exam, assignment, etc.).
     */
    public function gradeable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope for published grades.
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    /**
     * Scope for current academic year.
     */
    public function scopeCurrentYear($query)
    {
        $currentYear = AcademicYear::current();
        if ($currentYear) {
            return $query->where('academic_year_id', $currentYear->id);
        }
        return $query;
    }

    /**
     * Scope for current term.
     */
    public function scopeCurrentTerm($query)
    {
        $currentTerm = AcademicTerm::current();
        if ($currentTerm) {
            return $query->where('academic_term_id', $currentTerm->id);
        }
        return $query;
    }

    /**
     * Get status badge color.
     */
    public function getStatusBadgeColorAttribute(): string
    {
        return match($this->status) {
            'draft' => 'badge-gray',
            'published' => 'badge-green',
            'revised' => 'badge-yellow',
            default => 'badge-gray',
        };
    }

    /**
     * Get grade badge color based on percentage.
     */
    public function getGradeBadgeColorAttribute(): string
    {
        if ($this->percentage >= 90) return 'badge-green';
        if ($this->percentage >= 80) return 'badge-blue';
        if ($this->percentage >= 70) return 'badge-yellow';
        if ($this->percentage >= 60) return 'badge-orange';
        return 'badge-red';
    }

    /**
     * Get formatted percentage.
     */
    public function getFormattedPercentageAttribute(): string
    {
        return number_format($this->percentage, 1) . '%';
    }

    /**
     * Get formatted marks.
     */
    public function getFormattedMarksAttribute(): string
    {
        return number_format($this->marks_obtained, 1) . '/' . number_format($this->total_marks, 1);
    }

    /**
     * Check if grade is passing.
     */
    public function getIsPassingAttribute(): bool
    {
        // Get passing marks from the gradeable item
        $passingMarks = 0;
        
        if ($this->gradeable_type === 'App\Models\Exam') {
            $passingMarks = $this->gradeable->passing_marks ?? 0;
        } elseif ($this->gradeable_type === 'App\Models\Assignment') {
            // For assignments, assume 60% is passing
            $passingMarks = $this->total_marks * 0.6;
        }

        return $this->marks_obtained >= $passingMarks;
    }

    /**
     * Calculate and update percentage, letter grade, and grade points.
     */
    public function calculateGrade(): void
    {
        // Calculate percentage
        $this->percentage = ($this->marks_obtained / $this->total_marks) * 100;

        // Get letter grade and grade points from scale
        if ($this->gradeScale) {
            $this->letter_grade = $this->gradeScale->getLetterGrade($this->percentage);
            $this->grade_points = $this->gradeScale->getGradePoints($this->percentage);
        }

        $this->save();
    }
}
