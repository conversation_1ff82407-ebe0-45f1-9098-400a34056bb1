@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <x-page-header
        title="Edit Class"
        description="Update class information"
        :back-route="route('admin.academic.classes.show', $class)"
        back-label="Back to Class">
    </x-page-header>

    <!-- Form -->
    <div class="bg-white shadow rounded-lg">
        <form action="{{ route('admin.academic.classes.update', $class) }}" method="POST" class="space-y-6 p-6">
            @csrf
            @method('PUT')

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Class Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        Class Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           id="name" 
                           name="name" 
                           value="{{ old('name', $class->name) }}"
                           class="form-input @error('name') border-red-500 @enderror"
                           placeholder="e.g., Grade 1, Form 1"
                           required>
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Level -->
                <div>
                    <label for="level" class="block text-sm font-medium text-gray-700 mb-2">
                        Level <span class="text-red-500">*</span>
                    </label>
                    <select id="level" 
                            name="level" 
                            class="form-select @error('level') border-red-500 @enderror"
                            required>
                        <option value="">Select Level</option>
                        <option value="Pre-school" {{ old('level', $class->level) === 'Pre-school' ? 'selected' : '' }}>Pre-school</option>
                        <option value="Primary" {{ old('level', $class->level) === 'Primary' ? 'selected' : '' }}>Primary</option>
                        <option value="Secondary" {{ old('level', $class->level) === 'Secondary' ? 'selected' : '' }}>Secondary</option>
                    </select>
                    @error('level')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Sort Order -->
                <div>
                    <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">
                        Sort Order <span class="text-red-500">*</span>
                    </label>
                    <input type="number" 
                           id="sort_order" 
                           name="sort_order" 
                           value="{{ old('sort_order', $class->sort_order) }}"
                           min="0"
                           class="form-input @error('sort_order') border-red-500 @enderror"
                           required>
                    <p class="mt-1 text-xs text-gray-500">Lower numbers appear first in lists</p>
                    @error('sort_order')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Status -->
                <div class="flex items-center">
                    <input type="checkbox" 
                           id="is_active" 
                           name="is_active" 
                           value="1"
                           {{ old('is_active', $class->is_active) ? 'checked' : '' }}
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="is_active" class="ml-2 text-sm text-gray-700">
                        Active (class is available for enrollment)
                    </label>
                </div>
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                    Description
                </label>
                <textarea id="description" 
                          name="description" 
                          rows="3"
                          class="form-textarea @error('description') border-red-500 @enderror"
                          placeholder="Brief description of the class...">{{ old('description', $class->description) }}</textarea>
                @error('description')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="{{ route('admin.academic.classes.show', $class) }}" class="btn-cancel">
                    Cancel
                </a>
                <button type="submit" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Update Class
                </button>
            </div>
        </form>
    </div>
</div>
@endsection
