@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="Add New Student"
        description="Create a new student profile and account"
        :back-route="route('admin.students.index')"
        back-label="Back to Students">
    </x-page-header>

    <form method="POST" action="{{ route('admin.students.store') }}" class="space-y-6">
        @csrf
        
        <!-- Personal Information -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Personal Information</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Full Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                    <input type="text" name="name" id="name" value="{{ old('name') }}" 
                           class="form-input @error('name') border-red-500 @enderror" 
                           placeholder="Enter student's full name" required>
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Student ID -->
                <div>
                    <label for="student_id" class="block text-sm font-medium text-gray-700 mb-2">Student ID</label>
                    <input type="text" name="student_id" id="student_id" value="{{ old('student_id') }}" 
                           class="form-input @error('student_id') border-red-500 @enderror" 
                           placeholder="e.g., STU2024001" required>
                    @error('student_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Email -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                    <input type="email" name="email" id="email" value="{{ old('email') }}" 
                           class="form-input @error('email') border-red-500 @enderror" 
                           placeholder="<EMAIL>" required>
                    @error('email')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Phone -->
                <div>
                    <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                    <input type="text" name="phone" id="phone" value="{{ old('phone') }}" 
                           class="form-input @error('phone') border-red-500 @enderror" 
                           placeholder="+60123456789">
                    @error('phone')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Date of Birth -->
                <div>
                    <label for="date_of_birth" class="block text-sm font-medium text-gray-700 mb-2">Date of Birth</label>
                    <input type="date" name="date_of_birth" id="date_of_birth" value="{{ old('date_of_birth') }}" 
                           class="form-input @error('date_of_birth') border-red-500 @enderror" 
                           max="{{ date('Y-m-d', strtotime('-5 years')) }}" required>
                    @error('date_of_birth')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Gender -->
                <div>
                    <label for="gender" class="block text-sm font-medium text-gray-700 mb-2">Gender</label>
                    <select name="gender" id="gender" class="form-select @error('gender') border-red-500 @enderror" required>
                        <option value="">Select gender</option>
                        <option value="male" {{ old('gender') == 'male' ? 'selected' : '' }}>Male</option>
                        <option value="female" {{ old('gender') == 'female' ? 'selected' : '' }}>Female</option>
                    </select>
                    @error('gender')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Blood Group -->
                <div>
                    <label for="blood_group" class="block text-sm font-medium text-gray-700 mb-2">Blood Group</label>
                    <select name="blood_group" id="blood_group" class="form-select @error('blood_group') border-red-500 @enderror">
                        <option value="">Select blood group</option>
                        <option value="A+" {{ old('blood_group') == 'A+' ? 'selected' : '' }}>A+</option>
                        <option value="A-" {{ old('blood_group') == 'A-' ? 'selected' : '' }}>A-</option>
                        <option value="B+" {{ old('blood_group') == 'B+' ? 'selected' : '' }}>B+</option>
                        <option value="B-" {{ old('blood_group') == 'B-' ? 'selected' : '' }}>B-</option>
                        <option value="AB+" {{ old('blood_group') == 'AB+' ? 'selected' : '' }}>AB+</option>
                        <option value="AB-" {{ old('blood_group') == 'AB-' ? 'selected' : '' }}>AB-</option>
                        <option value="O+" {{ old('blood_group') == 'O+' ? 'selected' : '' }}>O+</option>
                        <option value="O-" {{ old('blood_group') == 'O-' ? 'selected' : '' }}>O-</option>
                    </select>
                    @error('blood_group')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Emergency Contact -->
                <div>
                    <label for="emergency_contact" class="block text-sm font-medium text-gray-700 mb-2">Emergency Contact</label>
                    <input type="text" name="emergency_contact" id="emergency_contact" value="{{ old('emergency_contact') }}" 
                           class="form-input @error('emergency_contact') border-red-500 @enderror" 
                           placeholder="+60123456789">
                    @error('emergency_contact')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Address -->
                <div class="md:col-span-2">
                    <label for="address" class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                    <textarea name="address" id="address" rows="3" 
                              class="form-textarea @error('address') border-red-500 @enderror"
                              placeholder="Enter complete address">{{ old('address') }}</textarea>
                    @error('address')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Medical Conditions -->
                <div class="md:col-span-2">
                    <label for="medical_conditions" class="block text-sm font-medium text-gray-700 mb-2">Medical Conditions</label>
                    <textarea name="medical_conditions" id="medical_conditions" rows="3" 
                              class="form-textarea @error('medical_conditions') border-red-500 @enderror"
                              placeholder="Any medical conditions, allergies, or special needs">{{ old('medical_conditions') }}</textarea>
                    @error('medical_conditions')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Academic Information -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Academic Information</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Class -->
                <div>
                    <label for="class" class="block text-sm font-medium text-gray-700 mb-2">Class</label>
                    <input type="text" name="class" id="class" value="{{ old('class') }}" 
                           class="form-input @error('class') border-red-500 @enderror" 
                           placeholder="e.g., Form 1, Year 7">
                    @error('class')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Section -->
                <div>
                    <label for="section" class="block text-sm font-medium text-gray-700 mb-2">Section</label>
                    <input type="text" name="section" id="section" value="{{ old('section') }}" 
                           class="form-input @error('section') border-red-500 @enderror" 
                           placeholder="e.g., A, B, Alpha">
                    @error('section')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Roll Number -->
                <div>
                    <label for="roll_number" class="block text-sm font-medium text-gray-700 mb-2">Roll Number</label>
                    <input type="text" name="roll_number" id="roll_number" value="{{ old('roll_number') }}" 
                           class="form-input @error('roll_number') border-red-500 @enderror" 
                           placeholder="e.g., 001, 025">
                    @error('roll_number')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Admission Date -->
                <div class="md:col-span-3">
                    <label for="admission_date" class="block text-sm font-medium text-gray-700 mb-2">Admission Date</label>
                    <input type="date" name="admission_date" id="admission_date" value="{{ old('admission_date', date('Y-m-d')) }}" 
                           class="form-input @error('admission_date') border-red-500 @enderror" 
                           max="{{ date('Y-m-d') }}" required>
                    @error('admission_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Account Information -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Account Information</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Password -->
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                    <input type="password" name="password" id="password" 
                           class="form-input @error('password') border-red-500 @enderror" 
                           placeholder="Enter password" required>
                    @error('password')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Confirm Password -->
                <div>
                    <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">Confirm Password</label>
                    <input type="password" name="password_confirmation" id="password_confirmation" 
                           class="form-input @error('password_confirmation') border-red-500 @enderror" 
                           placeholder="Confirm password" required>
                    @error('password_confirmation')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Guardian Information -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Guardian Information</h3>

            @if($guardians->count() > 0)
                <div class="space-y-3">
                    <label for="guardian-search" class="block text-sm font-medium text-gray-700">Select guardians for this student:</label>

                    <!-- Multiselect Search Component -->
                    <div x-data="multiselectSearch({
                        items: @json($guardians->map(function($guardian) {
                            return [
                                'id' => $guardian->id,
                                'name' => $guardian->user->name ?? '',
                                'subtitle' => ($guardian->relationship ?? '') . ' • ' . ($guardian->user->email ?? ''),
                                'searchText' => ($guardian->user->name ?? '') . ' ' . ($guardian->relationship ?? '') . ' ' . ($guardian->user->email ?? '')
                            ];
                        })->values()),
                        selected: @json(old('guardian_ids', [])),
                        name: 'guardian_ids[]',
                        placeholder: 'Search and select guardians...'
                    })" class="relative">

                        <!-- Search Input -->
                        <div class="relative">
                            <input
                                type="text"
                                x-model="searchQuery"
                                @focus="showDropdown = true"
                                @click.away="showDropdown = false"
                                class="form-input w-full pr-10"
                                :placeholder="placeholder"
                                autocomplete="off"
                            >
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>

                        <!-- Selected Items -->
                        <div x-show="selectedItems.length > 0" class="mt-2 flex flex-wrap gap-2">
                            <template x-for="item in selectedItems" :key="item.id">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800">
                                    <span x-text="item.name"></span>
                                    <button type="button" @click="removeItem(item.id)" class="ml-2 text-blue-600 hover:text-blue-800">
                                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    </button>
                                </span>
                            </template>
                        </div>

                        <!-- Dropdown -->
                        <div x-show="showDropdown && filteredItems.length > 0"
                             x-transition:enter="transition ease-out duration-100"
                             x-transition:enter-start="transform opacity-0 scale-95"
                             x-transition:enter-end="transform opacity-100 scale-100"
                             x-transition:leave="transition ease-in duration-75"
                             x-transition:leave-start="transform opacity-100 scale-100"
                             x-transition:leave-end="transform opacity-0 scale-95"
                             class="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none">
                            <template x-for="item in filteredItems" :key="item.id">
                                <div @click="toggleItem(item)"
                                     class="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-gray-50"
                                     :class="{ 'bg-blue-50': isSelected(item.id) }">
                                    <div class="flex items-center">
                                        <span class="font-medium block truncate" x-text="item.name"></span>
                                        <span x-show="isSelected(item.id)" class="absolute inset-y-0 right-0 flex items-center pr-4 text-blue-600">
                                            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                        </span>
                                    </div>
                                    <span class="text-gray-500 block truncate text-sm" x-text="item.subtitle"></span>
                                </div>
                            </template>
                        </div>

                        <!-- Hidden inputs for form submission -->
                        <template x-for="item in selectedItems" :key="item.id">
                            <input type="hidden" :name="name" :value="item.id">
                        </template>
                    </div>
                </div>
            @else
                <div class="text-center py-4">
                    <p class="text-sm text-gray-500">No guardians available. You can add guardians later.</p>
                </div>
            @endif

            @error('guardian_ids')
                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
            @enderror
        </div>

        <!-- Actions -->
        <div class="flex justify-end space-x-4">
            <a href="{{ route('admin.students.index') }}" class="btn-cancel">Cancel</a>
            <button type="submit" class="btn-primary">Create Student</button>
        </div>
    </form>
</div>

@push('scripts')
<script>
function multiselectSearch(config) {
    return {
        items: config.items || [],
        selectedItems: [],
        searchQuery: '',
        showDropdown: false,
        name: config.name || 'items[]',
        placeholder: config.placeholder || 'Search and select items...',

        init() {
            // Initialize selected items based on config.selected
            if (config.selected && config.selected.length > 0) {
                this.selectedItems = this.items.filter(item =>
                    config.selected.includes(parseInt(item.id))
                );
            }
        },

        get filteredItems() {
            if (!this.searchQuery) {
                return this.items.filter(item => !this.isSelected(item.id));
            }

            return this.items.filter(item => {
                const matchesSearch = item.searchText.toLowerCase().includes(this.searchQuery.toLowerCase());
                const notSelected = !this.isSelected(item.id);
                return matchesSearch && notSelected;
            });
        },

        isSelected(itemId) {
            return this.selectedItems.some(item => item.id == itemId);
        },

        toggleItem(item) {
            if (this.isSelected(item.id)) {
                this.removeItem(item.id);
            } else {
                this.selectedItems.push(item);
                this.searchQuery = '';
                this.showDropdown = false;
            }
        },

        removeItem(itemId) {
            this.selectedItems = this.selectedItems.filter(item => item.id != itemId);
        }
    };
}
</script>
@endpush
@endsection
