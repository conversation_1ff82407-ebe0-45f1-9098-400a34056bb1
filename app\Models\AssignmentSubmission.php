<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphOne;

class AssignmentSubmission extends Model
{
    use HasFactory;

    protected $fillable = [
        'assignment_id',
        'student_id',
        'submission_text',
        'attachments',
        'submitted_at',
        'is_late',
        'teacher_feedback',
        'status',
    ];

    protected $casts = [
        'attachments' => 'array',
        'submitted_at' => 'datetime',
        'is_late' => 'boolean',
    ];

    /**
     * Get the assignment.
     */
    public function assignment(): BelongsTo
    {
        return $this->belongsTo(Assignment::class);
    }

    /**
     * Get the student.
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * Get the grade for this submission.
     */
    public function grade(): MorphOne
    {
        return $this->morphOne(Grade::class, 'gradeable');
    }

    /**
     * Get status badge color.
     */
    public function getStatusBadgeColorAttribute(): string
    {
        return match($this->status) {
            'submitted' => 'badge-blue',
            'graded' => 'badge-green',
            'returned' => 'badge-purple',
            default => 'badge-gray',
        };
    }

    /**
     * Get formatted submission date.
     */
    public function getFormattedSubmissionDateAttribute(): string
    {
        return $this->submitted_at->format('M d, Y g:i A');
    }

    /**
     * Check if submission has attachments.
     */
    public function getHasAttachmentsAttribute(): bool
    {
        return !empty($this->attachments);
    }

    /**
     * Get attachment count.
     */
    public function getAttachmentCountAttribute(): int
    {
        return count($this->attachments ?? []);
    }

    /**
     * Get late submission penalty.
     */
    public function getLatePenaltyAttribute(): float
    {
        if (!$this->is_late || !$this->assignment->late_penalty_percentage) {
            return 0;
        }

        return $this->assignment->late_penalty_percentage;
    }
}
