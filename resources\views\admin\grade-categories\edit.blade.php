@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <x-page-header
        title="Edit Grade Category"
        description="Update grade category information and settings"
        :back-route="route('admin.grading.grade-categories.show', $gradeCategory)"
        back-label="Back to Grade Category">
    </x-page-header>

    <!-- Edit Form -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div class="lg:col-span-2">
            <div class="card">
                <div class="card-body">
                    <h3 class="text-lg font-medium text-gray-900 mb-6">Grade Category Information</h3>

                    <form action="{{ route('admin.grading.grade-categories.update', $gradeCategory) }}" method="POST" class="space-y-6">
                        @csrf
                        @method('PUT')

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="name" class="form-label">Name <span class="text-red-500">*</span></label>
                                <input type="text"
                                       class="form-input @error('name') border-red-300 @enderror"
                                       id="name" name="name" value="{{ old('name', $gradeCategory->name) }}" required>
                                @error('name')
                                    <p class="form-error">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="code" class="form-label">Code <span class="text-red-500">*</span></label>
                                <input type="text"
                                       class="form-input @error('code') border-red-300 @enderror"
                                       id="code" name="code" value="{{ old('code', $gradeCategory->code) }}" required>
                                @error('code')
                                    <p class="form-error">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div>
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-textarea @error('description') border-red-300 @enderror"
                                      id="description" name="description" rows="3">{{ old('description', $gradeCategory->description) }}</textarea>
                            @error('description')
                                <p class="form-error">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <label for="weight_percentage" class="form-label">Weight Percentage <span class="text-red-500">*</span></label>
                                <div class="relative">
                                    <input type="number"
                                           class="form-input pr-12 @error('weight_percentage') border-red-300 @enderror"
                                           id="weight_percentage" name="weight_percentage"
                                           value="{{ old('weight_percentage', $gradeCategory->weight_percentage) }}"
                                           min="0" max="100" step="0.01" required>
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 text-sm">%</span>
                                    </div>
                                </div>
                                @error('weight_percentage')
                                    <p class="form-error">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="color" class="form-label">Color <span class="text-red-500">*</span></label>
                                <input type="color"
                                       class="form-input h-10 @error('color') border-red-300 @enderror"
                                       id="color" name="color" value="{{ old('color', $gradeCategory->color) }}" required>
                                @error('color')
                                    <p class="form-error">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="sort_order" class="form-label">Sort Order <span class="text-red-500">*</span></label>
                                <input type="number"
                                       class="form-input @error('sort_order') border-red-300 @enderror"
                                       id="sort_order" name="sort_order"
                                       value="{{ old('sort_order', $gradeCategory->sort_order) }}"
                                       min="0" required>
                                @error('sort_order')
                                    <p class="form-error">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox"
                                   class="form-checkbox"
                                   id="is_active" name="is_active" value="1"
                                   {{ old('is_active', $gradeCategory->is_active) ? 'checked' : '' }}>
                            <label for="is_active" class="ml-2 text-sm text-gray-700">
                                Active
                            </label>
                        </div>

                        <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                            <a href="{{ route('admin.grading.grade-categories.show', $gradeCategory) }}"
                               class="btn-cancel" style="cursor: pointer;">
                                Cancel
                            </a>
                            <button type="submit" class="btn-primary" style="cursor: pointer;">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Update Grade Category
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Current Usage -->
            <div class="card">
                <div class="card-body">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Current Usage</h3>

                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                        <div class="flex items-center mb-2">
                            <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <h4 class="text-sm font-medium text-blue-900">Usage Information</h4>
                        </div>
                        <ul class="text-sm text-blue-800 space-y-1">
                            <li>• Used in {{ $gradeCategory->exams()->count() }} exams</li>
                            <li>• Used in {{ $gradeCategory->assignments()->count() }} assignments</li>
                            <li>• Has {{ $gradeCategory->grades()->count() }} grades recorded</li>
                        </ul>
                    </div>

                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <svg class="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <h4 class="text-sm font-medium text-yellow-900">Important Notes</h4>
                        </div>
                        <ul class="text-sm text-yellow-800 space-y-1">
                            <li>• Code must be unique across all categories</li>
                            <li>• Weight percentage must be between 0-100</li>
                            <li>• Changing weight affects grade calculations</li>
                            <li>• Deactivating will hide from new assessments</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate code from name if code is empty
    const nameInput = document.getElementById('name');
    const codeInput = document.getElementById('code');

    nameInput.addEventListener('input', function() {
        const name = this.value;
        const currentCode = codeInput.value;

        // Only auto-generate if code field is empty
        if (!currentCode) {
            const code = name.toUpperCase().replace(/[^A-Z0-9]/g, '').substring(0, 10);
            codeInput.value = code;
        }
    });
});
</script>
@endsection
