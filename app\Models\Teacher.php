<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Teacher extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'employee_id',
        'qualification',
        'specialization',
        'hire_date',
        'salary',
        'subjects',
        'classes',
    ];

    protected $casts = [
        'hire_date' => 'date',
        'salary' => 'decimal:2',
        'subjects' => 'array',
        'classes' => 'array',
    ];

    /**
     * Get the user that owns the teacher profile
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the invoices created by this teacher
     */
    public function invoices()
    {
        return $this->hasMany(Invoice::class, 'created_by', 'user_id');
    }

    /**
     * Get the attendance records for this teacher
     */
    public function attendances()
    {
        return $this->hasMany(TeacherAttendance::class);
    }

    /**
     * Get the subjects assigned to this teacher
     */
    public function assignedSubjects()
    {
        return $this->belongsToMany(Subject::class, 'teacher_subjects', 'teacher_id', 'subject_id')
                    ->withPivot('class_id', 'section_id', 'is_primary')
                    ->withTimestamps();
    }

    /**
     * Get the classes this teacher teaches
     */
    public function assignedClasses()
    {
        return $this->belongsToMany(ClassModel::class, 'teacher_subjects', 'teacher_id', 'class_id')
                    ->withPivot('subject_id', 'section_id', 'is_primary')
                    ->withTimestamps()
                    ->distinct();
    }

    /**
     * Get the sections this teacher teaches
     */
    public function assignedSections()
    {
        return $this->belongsToMany(Section::class, 'teacher_subjects', 'teacher_id', 'section_id')
                    ->withPivot('subject_id', 'class_id', 'is_primary')
                    ->withTimestamps()
                    ->whereNotNull('teacher_subjects.section_id');
    }

    /**
     * Get all teaching assignments (subjects + classes + sections)
     */
    public function teachingAssignments()
    {
        return $this->hasMany(TeacherSubject::class);
    }

    /**
     * Get students this teacher teaches
     */
    public function students()
    {
        return Student::whereHas('currentEnrollment', function ($query) {
            $query->whereIn('class_id', $this->assignedClasses()->pluck('classes.id'))
                  ->whereIn('section_id', $this->assignedSections()->pluck('sections.id'));
        });
    }

    /**
     * Get the full name of the teacher
     */
    public function getFullNameAttribute()
    {
        return $this->user->name;
    }

    /**
     * Get the teacher's years of experience
     */
    public function getYearsOfExperienceAttribute()
    {
        return $this->hire_date ? $this->hire_date->diffInYears(now()) : 0;
    }

    /**
     * Get the teacher's subjects as a formatted string
     */
    public function getSubjectsStringAttribute()
    {
        return is_array($this->subjects) ? implode(', ', $this->subjects) : 'No subjects assigned';
    }

    /**
     * Get the teacher's classes as a formatted string
     */
    public function getClassesStringAttribute()
    {
        return is_array($this->classes) ? implode(', ', $this->classes) : 'No classes assigned';
    }

    /**
     * Check if teacher is active
     */
    public function getIsActiveAttribute()
    {
        return $this->user->is_active;
    }

    /**
     * Get total invoices created by teacher
     */
    public function getTotalInvoicesAttribute()
    {
        return $this->invoices()->count();
    }

    /**
     * Get total amount invoiced by teacher
     */
    public function getTotalAmountInvoicedAttribute()
    {
        return $this->invoices()->sum('total_amount');
    }

    /**
     * Scope to filter active teachers
     */
    public function scopeActive($query)
    {
        return $query->whereHas('user', function ($q) {
            $q->where('is_active', true);
        });
    }

    /**
     * Scope to filter by specialization
     */
    public function scopeBySpecialization($query, $specialization)
    {
        return $query->where('specialization', 'like', "%{$specialization}%");
    }

    /**
     * Scope to filter by qualification
     */
    public function scopeByQualification($query, $qualification)
    {
        return $query->where('qualification', 'like', "%{$qualification}%");
    }

    /**
     * Scope to search teachers
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->whereHas('user', function ($userQuery) use ($search) {
                $userQuery->where('name', 'like', "%{$search}%")
                          ->orWhere('email', 'like', "%{$search}%")
                          ->orWhere('phone', 'like', "%{$search}%")
                          ->orWhere('address', 'like', "%{$search}%");
            })->orWhere('employee_id', 'like', "%{$search}%")
              ->orWhere('specialization', 'like', "%{$search}%")
              ->orWhere('qualification', 'like', "%{$search}%")
              ->orWhere('department', 'like', "%{$search}%")
              ->orWhere('experience_years', 'like', "%{$search}%")
              ->orWhere('salary', 'like', "%{$search}%");
        });
    }

    /**
     * Scope to filter teachers by class assignment
     */
    public function scopeByClass($query, $class)
    {
        return $query->whereJsonContains('classes', $class);
    }

    /**
     * Scope to filter teachers by subject
     */
    public function scopeBySubject($query, $subject)
    {
        return $query->whereJsonContains('subjects', $subject);
    }
}
