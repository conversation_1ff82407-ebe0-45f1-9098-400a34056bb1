@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <x-page-header
        title="Edit Subject"
        description="Update subject information"
        :back-route="route('admin.academic.subjects.show', $subject)"
        back-label="Back to Subject">
    </x-page-header>

    <!-- Form -->
    <div class="bg-white shadow rounded-lg">
        <form action="{{ route('admin.academic.subjects.update', $subject) }}" method="POST" class="space-y-6 p-6">
            @csrf
            @method('PUT')

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Subject Code -->
                <div>
                    <label for="subject_code" class="block text-sm font-medium text-gray-700 mb-2">
                        Subject Code <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           id="subject_code" 
                           name="subject_code" 
                           value="{{ old('subject_code', $subject->subject_code) }}"
                           class="form-input @error('subject_code') border-red-500 @enderror"
                           placeholder="e.g., MATH101, ENG201"
                           required>
                    @error('subject_code')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Subject Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        Subject Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           id="name" 
                           name="name" 
                           value="{{ old('name', $subject->name) }}"
                           class="form-input @error('name') border-red-500 @enderror"
                           placeholder="e.g., Mathematics, English Literature"
                           required>
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Category -->
                <div>
                    <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                        Category <span class="text-red-500">*</span>
                    </label>
                    <select id="category" 
                            name="category" 
                            class="form-select @error('category') border-red-500 @enderror"
                            required>
                        <option value="">Select Category</option>
                        <option value="Core" {{ old('category', $subject->category) === 'Core' ? 'selected' : '' }}>Core</option>
                        <option value="Elective" {{ old('category', $subject->category) === 'Elective' ? 'selected' : '' }}>Elective</option>
                        <option value="Extra-curricular" {{ old('category', $subject->category) === 'Extra-curricular' ? 'selected' : '' }}>Extra-curricular</option>
                    </select>
                    @error('category')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Credits -->
                <div>
                    <label for="credits" class="block text-sm font-medium text-gray-700 mb-2">
                        Credits <span class="text-red-500">*</span>
                    </label>
                    <input type="number" 
                           id="credits" 
                           name="credits" 
                           value="{{ old('credits', $subject->credits) }}"
                           min="1" 
                           max="10"
                           class="form-input @error('credits') border-red-500 @enderror"
                           required>
                    @error('credits')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                    Description
                </label>
                <textarea id="description" 
                          name="description" 
                          rows="3"
                          class="form-textarea @error('description') border-red-500 @enderror"
                          placeholder="Brief description of the subject...">{{ old('description', $subject->description) }}</textarea>
                @error('description')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Grade Levels -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Grade Levels
                </label>
                <p class="text-sm text-gray-500 mb-3">Select which grade levels this subject is available for (leave empty for all grades)</p>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                    @foreach($classes as $class)
                        <label class="flex items-center">
                            <input type="checkbox" 
                                   name="grade_levels[]" 
                                   value="{{ $class->name }}"
                                   {{ in_array($class->name, old('grade_levels', $subject->grade_levels ?? [])) ? 'checked' : '' }}
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">{{ $class->name }}</span>
                        </label>
                    @endforeach
                </div>
                @error('grade_levels')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Prerequisites -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Prerequisites
                </label>
                <p class="text-sm text-gray-500 mb-3">Select subjects that are required before taking this subject</p>

                @if($existingSubjects->count() > 0)
                    <!-- Multiselect Search Component -->
                    <div x-data="multiselectSearch()" class="relative"
                         x-init="
                            items = [
                                @foreach($existingSubjects as $existingSubject)
                                {
                                    id: {{ $existingSubject->id }},
                                    name: '{{ addslashes($existingSubject->name) }}',
                                    subtitle: '{{ addslashes($existingSubject->subject_code . ' • ' . $existingSubject->category . ' • ' . $existingSubject->credits . ' credits') }}',
                                    searchText: '{{ addslashes($existingSubject->name . ' ' . $existingSubject->subject_code . ' ' . $existingSubject->category) }}'
                                },
                                @endforeach
                            ];
                            selected = [{{ implode(',', old('prerequisites', $subject->prerequisites ?? [])) }}];
                            name = 'prerequisites[]';
                            placeholder = 'Search and select prerequisite subjects...';
                            init();
                         ">

                        <!-- Search Input -->
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                            <input
                                type="text"
                                x-model="searchQuery"
                                @focus="showDropdown = true"
                                @click.away="showDropdown = false"
                                class="search-input"
                                :placeholder="placeholder"
                                autocomplete="off"
                            >
                        </div>

                        <!-- Selected Items -->
                        <div x-show="selectedItems.length > 0" class="mt-3">
                            <div class="flex flex-wrap gap-2">
                                <template x-for="item in selectedItems" :key="item.id">
                                    <span class="inline-flex items-center px-3 py-1 rounded-md text-sm bg-blue-100 text-blue-800">
                                        <span x-text="item.name"></span>
                                        <button type="button" @click="removeItem(item.id)" class="ml-2 text-blue-600 hover:text-blue-800">
                                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                        </button>
                                    </span>
                                </template>
                            </div>
                        </div>

                        <!-- Dropdown -->
                        <div x-show="showDropdown && filteredItems.length > 0"
                             x-transition:enter="transition ease-out duration-100"
                             x-transition:enter-start="transform opacity-0 scale-95"
                             x-transition:enter-end="transform opacity-100 scale-100"
                             x-transition:leave="transition ease-in duration-75"
                             x-transition:leave-start="transform opacity-100 scale-100"
                             x-transition:leave-end="transform opacity-0 scale-95"
                             class="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base border border-gray-200 overflow-auto focus:outline-none sm:text-sm">
                            <template x-for="item in filteredItems" :key="item.id">
                                <div @click="toggleItem(item)"
                                     class="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-blue-50">
                                    <div class="flex items-center">
                                        <span class="font-medium block truncate" x-text="item.name"></span>
                                    </div>
                                    <span class="text-gray-500 text-sm block truncate" x-text="item.subtitle"></span>
                                </div>
                            </template>
                        </div>

                        <!-- Hidden inputs for selected items -->
                        <template x-for="item in selectedItems" :key="item.id">
                            <input type="hidden" :name="name" :value="item.id">
                        </template>
                    </div>
                @else
                    <div class="text-center py-4">
                        <p class="text-sm text-gray-500">No other subjects available for prerequisites.</p>
                    </div>
                @endif

                @error('prerequisites')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Status -->
            <div>
                <div class="flex items-center">
                    <input type="checkbox" 
                           id="is_active" 
                           name="is_active" 
                           value="1"
                           {{ old('is_active', $subject->is_active) ? 'checked' : '' }}
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="is_active" class="ml-2 text-sm text-gray-700">
                        Active (subject is available for assignment)
                    </label>
                </div>
                @error('is_active')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                <!-- Delete Button (Left Side) -->
                <button type="button"
                        onclick="handleDeleteSubject({{ $subject->id }}, '{{ addslashes($subject->name) }}')"
                        class="btn-danger"
                        title="Delete Subject">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    Delete Subject
                </button>

                <!-- Update and Cancel Buttons (Right Side) -->
                <div class="flex items-center space-x-3">
                    <a href="{{ route('admin.academic.subjects.show', $subject) }}" class="btn-cancel">
                        Cancel
                    </a>
                    <button type="submit" class="btn-primary">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Update Subject
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
function multiselectSearch() {
    return {
        items: [],
        selectedItems: [],
        searchQuery: '',
        showDropdown: false,
        name: 'items[]',
        placeholder: 'Search and select items...',
        selected: [],

        init() {
            // Initialize selected items based on this.selected
            if (this.selected && this.selected.length > 0) {
                this.selectedItems = this.items.filter(item =>
                    this.selected.includes(item.id)
                );
            }
        },

        get filteredItems() {
            if (!this.searchQuery) {
                return this.items.filter(item => !this.isSelected(item.id));
            }

            return this.items.filter(item => {
                const matchesSearch = item.searchText.toLowerCase().includes(this.searchQuery.toLowerCase());
                const notSelected = !this.isSelected(item.id);
                return matchesSearch && notSelected;
            });
        },

        isSelected(itemId) {
            return this.selectedItems.some(item => item.id == itemId);
        },

        toggleItem(item) {
            if (this.isSelected(item.id)) {
                this.removeItem(item.id);
            } else {
                this.selectedItems.push(item);
                this.searchQuery = '';
                this.showDropdown = false;
            }
        },

        removeItem(itemId) {
            this.selectedItems = this.selectedItems.filter(item => item.id != itemId);
        }
    }
}

// Enhanced delete functionality for edit page
async function handleDeleteSubject(subjectId, subjectName) {
    try {
        // First check dependencies
        const response = await fetch(`{{ route('admin.academic.subjects.check-dependencies', ':id') }}`.replace(':id', subjectId));
        const data = await response.json();

        if (!data.success) {
            alert('Error checking dependencies: ' + data.message);
            return;
        }

        if (data.has_dependencies) {
            // Show dependency details and ask for confirmation
            showDependencyModal(subjectId, subjectName, data.dependencies);
        } else {
            // No dependencies, proceed with regular delete
            const confirmed = await confirmModal({
                title: 'Delete Subject',
                message: `Are you sure you want to delete subject "${subjectName}"? This action can be undone.`,
                confirmText: 'Delete',
                cancelText: 'Cancel',
                type: 'danger'
            });

            if (confirmed) {
                submitDeleteForm(subjectId, 'DELETE');
            }
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Error checking subject dependencies. Please try again.');
    }
}

function showDependencyModal(subjectId, subjectName, dependencies) {
    const summary = dependencies.summary;
    let dependencyDetails = '';

    if (summary.classes_count > 0) {
        dependencyDetails += `<li><strong>${summary.classes_count} Classes:</strong> `;
        dependencyDetails += dependencies.classes.map(c => c.name).join(', ') + '</li>';
    }

    if (summary.teachers_count > 0) {
        dependencyDetails += `<li><strong>${summary.teachers_count} Teachers:</strong> `;
        dependencyDetails += dependencies.teachers.map(t => t.name).join(', ') + '</li>';
    }

    if (summary.dependent_subjects_count > 0) {
        dependencyDetails += `<li><strong>${summary.dependent_subjects_count} Dependent Subjects:</strong> `;
        dependencyDetails += dependencies.dependent_subjects.map(s => s.name).join(', ') + '</li>';
    }

    if (summary.students_count > 0) {
        dependencyDetails += `<li><strong>${summary.students_count} Active Student Enrollments</strong></li>';
    }

    const modalHtml = `
        <div id="dependencyModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-6 w-96 shadow-xl rounded-lg bg-white max-h-96 overflow-y-auto">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Subject Has Dependencies</h3>
                <p class="text-sm text-gray-600 mb-4">
                    Subject "${subjectName}" is currently being used by:
                </p>
                <ul class="list-disc list-inside text-sm text-gray-700 mb-6 space-y-1">
                    ${dependencyDetails}
                </ul>
                <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
                    <div class="flex">
                        <svg class="h-5 w-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <div>
                            <h4 class="text-sm font-medium text-yellow-800">Warning</h4>
                            <p class="text-sm text-yellow-700 mt-1">
                                Force deleting will permanently remove the subject and all its dependencies. This action cannot be undone.
                            </p>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3">
                    <button onclick="closeDependencyModal()" class="btn-cancel">Cancel</button>
                    <button onclick="forceDeleteSubject(${subjectId})" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                        Force Delete
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
}

function closeDependencyModal() {
    const modal = document.getElementById('dependencyModal');
    if (modal) {
        modal.remove();
    }
}

function forceDeleteSubject(subjectId) {
    closeDependencyModal();
    submitDeleteForm(subjectId, 'FORCE_DELETE');
}

function submitDeleteForm(subjectId, deleteType) {
    const form = document.createElement('form');
    form.method = 'POST';

    if (deleteType === 'FORCE_DELETE') {
        form.action = `{{ route('admin.academic.subjects.force-destroy', ':id') }}`.replace(':id', subjectId);
    } else {
        form.action = `{{ route('admin.academic.subjects.destroy', ':id') }}`.replace(':id', subjectId);
    }

    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = '_token';
    csrfInput.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    const methodInput = document.createElement('input');
    methodInput.type = 'hidden';
    methodInput.name = '_method';
    methodInput.value = 'DELETE';

    form.appendChild(csrfInput);
    form.appendChild(methodInput);
    document.body.appendChild(form);
    form.submit();
}
</script>
@endpush

@endsection
