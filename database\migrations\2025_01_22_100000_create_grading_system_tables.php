<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Grade Scales - Define different grading scales (A-F, 1-100, etc.)
        Schema::create('grade_scales', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // A-F Scale, Percentage Scale, etc.
            $table->string('type'); // letter, percentage, points
            $table->json('scale_values'); // [{"min": 90, "max": 100, "grade": "A", "points": 4.0}]
            $table->boolean('is_default')->default(false);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // Grade Categories - Types of assessments
        Schema::create('grade_categories', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Quiz, Test, Assignment, Final Exam, etc.
            $table->string('code')->unique(); // QUIZ, TEST, ASSIGN, FINAL
            $table->text('description')->nullable();
            $table->decimal('weight_percentage', 5, 2)->default(0); // Weight in final grade calculation
            $table->string('color', 7)->default('#3b82f6'); // Hex color for UI
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });

        // Exams/Assessments
        Schema::create('exams', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('exam_code')->unique();
            $table->text('description')->nullable();
            $table->foreignId('academic_year_id')->constrained()->onDelete('cascade');
            $table->foreignId('academic_term_id')->constrained()->onDelete('cascade');
            $table->foreignId('grade_category_id')->constrained()->onDelete('cascade');
            $table->foreignId('grade_scale_id')->constrained()->onDelete('cascade');
            $table->date('exam_date');
            $table->time('start_time')->nullable();
            $table->time('end_time')->nullable();
            $table->integer('duration_minutes')->nullable(); // Exam duration
            $table->decimal('total_marks', 8, 2);
            $table->decimal('passing_marks', 8, 2);
            $table->enum('status', ['draft', 'scheduled', 'ongoing', 'completed', 'cancelled'])->default('draft');
            $table->text('instructions')->nullable();
            $table->json('target_classes')->nullable(); // Which classes this exam is for
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->boolean('is_published')->default(false); // Whether results are published
            $table->timestamp('published_at')->nullable();
            $table->timestamps();
        });

        // Exam Subjects - Link exams to specific subjects
        Schema::create('exam_subjects', function (Blueprint $table) {
            $table->id();
            $table->foreignId('exam_id')->constrained()->onDelete('cascade');
            $table->foreignId('subject_id')->constrained()->onDelete('cascade');
            $table->foreignId('teacher_id')->nullable()->constrained('users')->onDelete('set null');
            $table->decimal('subject_total_marks', 8, 2);
            $table->decimal('subject_passing_marks', 8, 2);
            $table->time('subject_start_time')->nullable();
            $table->time('subject_end_time')->nullable();
            $table->integer('subject_duration_minutes')->nullable();
            $table->text('subject_instructions')->nullable();
            $table->timestamps();

            $table->unique(['exam_id', 'subject_id']);
        });

        // Assignments/Homework
        Schema::create('assignments', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('assignment_code')->unique();
            $table->text('description')->nullable();
            $table->foreignId('academic_year_id')->constrained()->onDelete('cascade');
            $table->foreignId('academic_term_id')->constrained()->onDelete('cascade');
            $table->foreignId('subject_id')->constrained()->onDelete('cascade');
            $table->foreignId('class_id')->constrained('classes')->onDelete('cascade');
            $table->foreignId('section_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('teacher_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('grade_category_id')->constrained()->onDelete('cascade');
            $table->foreignId('grade_scale_id')->constrained()->onDelete('cascade');
            $table->date('assigned_date');
            $table->date('due_date');
            $table->time('due_time')->nullable();
            $table->decimal('total_marks', 8, 2);
            $table->decimal('passing_marks', 8, 2);
            $table->text('instructions')->nullable();
            $table->json('attachments')->nullable(); // File attachments
            $table->boolean('allow_late_submission')->default(false);
            $table->decimal('late_penalty_percentage', 5, 2)->default(0);
            $table->integer('max_attempts')->nullable();
            $table->enum('submission_type', ['online', 'offline', 'both'])->default('online');
            $table->enum('status', ['draft', 'active', 'completed', 'cancelled'])->default('draft');
            $table->boolean('is_published')->default(false);
            $table->timestamps();
        });

        // Assignment Submissions
        Schema::create('assignment_submissions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('assignment_id')->constrained()->onDelete('cascade');
            $table->foreignId('student_id')->constrained()->onDelete('cascade');
            $table->text('submission_text')->nullable();
            $table->json('attachments')->nullable(); // Submitted files
            $table->timestamp('submitted_at');
            $table->boolean('is_late')->default(false);
            $table->text('teacher_feedback')->nullable();
            $table->enum('status', ['submitted', 'graded', 'returned'])->default('submitted');
            $table->timestamps();

            $table->unique(['assignment_id', 'student_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assignment_submissions');
        Schema::dropIfExists('assignments');
        Schema::dropIfExists('exam_subjects');
        Schema::dropIfExists('exams');
        Schema::dropIfExists('grade_categories');
        Schema::dropIfExists('grade_scales');
    }
};
