@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <x-page-header
        title="Exam Details"
        description="View exam information and manage exam settings"
        :back-route="route('admin.grading.exams.index')"
        back-label="Back to Exams">

        <div class="flex items-center space-x-3">
            <a href="{{ route('admin.grading.exams.edit', $exam) }}" class="btn-secondary" style="cursor: pointer;">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                </svg>
                Edit Exam
            </a>
        </div>
    </x-page-header>

    <!-- Exam <PERSON> -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 bg-gradient-to-r from-blue-500 to-blue-600">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-white">{{ $exam->title }}</h1>
                    <p class="text-blue-100">{{ $exam->exam_code }}</p>
                </div>
                <div class="flex items-center space-x-3">
                    @switch($exam->status)
                        @case('draft')
                            <span class="badge badge-gray">Draft</span>
                            @break
                        @case('scheduled')
                            <span class="badge badge-blue">Scheduled</span>
                            @break
                        @case('active')
                            <span class="badge badge-green">Active</span>
                            @break
                        @case('completed')
                            <span class="badge badge-purple">Completed</span>
                            @break
                        @case('cancelled')
                            <span class="badge badge-red">Cancelled</span>
                            @break
                    @endswitch

                    @if($exam->is_published)
                        <span class="badge badge-green">Published</span>
                    @else
                        <span class="badge badge-yellow">Not Published</span>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Exam Details -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Exam Information</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Title</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $exam->title }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Exam Code</label>
                        <p class="mt-1">
                            <span class="badge badge-blue">{{ $exam->exam_code }}</span>
                        </p>
                    </div>

                    @if($exam->description)
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">Description</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $exam->description }}</p>
                        </div>
                    @endif

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Academic Year</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $exam->academicYear->name }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Academic Term</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $exam->academicTerm->name }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Grade Category</label>
                        <p class="mt-1">
                            <span class="badge text-white" style="background-color: {{ $exam->gradeCategory->color }}">
                                {{ $exam->gradeCategory->name }}
                            </span>
                        </p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Grade Scale</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $exam->gradeScale ? $exam->gradeScale->name : 'Default' }}</p>
                    </div>
                </div>
            </div>

            <!-- Schedule & Marks -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Schedule & Marks</h3>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Exam Date</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $exam->exam_date->format('M d, Y') }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Start Time</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $exam->start_time ? $exam->start_time->format('g:i A') : 'Not set' }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">End Time</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $exam->end_time ? $exam->end_time->format('g:i A') : 'Not set' }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Duration</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $exam->duration_minutes ? $exam->duration_minutes . ' minutes' : 'Not set' }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Total Marks</label>
                        <p class="mt-1 text-sm text-gray-900">{{ number_format($exam->total_marks, 1) }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Passing Marks</label>
                        <p class="mt-1 text-sm text-gray-900">{{ number_format($exam->passing_marks, 1) }}</p>
                    </div>
                </div>

                @if($exam->instructions)
                    <div class="mt-6">
                        <label class="block text-sm font-medium text-gray-700">Instructions</label>
                        <div class="mt-1 bg-gray-50 border border-gray-200 rounded-lg p-4">
                            <div class="text-sm text-gray-900 whitespace-pre-line">{{ $exam->instructions }}</div>
                        </div>
                    </div>
                @endif

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Created By</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $exam->creator->name }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Created</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $exam->created_at->format('M d, Y g:i A') }}</p>
                    </div>
                </div>
            </div>

            <!-- Target Classes -->
            @if($targetClasses->count() > 0)
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Target Classes</h3>
                <div class="flex flex-wrap gap-2">
                    @foreach($targetClasses as $class)
                        <span class="badge badge-indigo">{{ $class->name }}</span>
                    @endforeach
                </div>
            </div>
            @endif

            <!-- Exam Subjects -->
            @if($exam->examSubjects->count() > 0)
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Exam Subjects</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teacher</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Marks</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Passing Marks</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($exam->examSubjects as $examSubject)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $examSubject->subject->name }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $examSubject->teacher ? $examSubject->teacher->name : 'Not assigned' }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ number_format($examSubject->subject_total_marks, 1) }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ number_format($examSubject->subject_passing_marks, 1) }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $examSubject->subject_duration_minutes ? $examSubject->subject_duration_minutes . ' min' : '-' }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Actions -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>

                <div class="space-y-3">
                    <form method="POST" action="{{ route('admin.grading.exams.publish', $exam) }}" class="w-full">
                        @csrf
                        <button type="submit"
                                style="cursor: pointer;"
                                class="w-full {{ $exam->is_published ? 'bg-yellow-600 hover:bg-yellow-700' : 'bg-green-600 hover:bg-green-700' }} text-white px-4 py-2 rounded-lg transition-colors text-center"
                                onclick="return confirm('Are you sure you want to {{ $exam->is_published ? 'unpublish' : 'publish' }} this exam?')">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                @if($exam->is_published)
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                                @else
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                @endif
                            </svg>
                            {{ $exam->is_published ? 'Unpublish' : 'Publish' }}
                        </button>
                    </form>

                    @if($exam->status === 'draft')
                        <form method="POST" action="{{ route('admin.grading.exams.schedule', $exam) }}" class="w-full">
                            @csrf
                            <button type="submit"
                                    style="cursor: pointer;"
                                    class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors text-center"
                                    onclick="return confirm('Are you sure you want to schedule this exam?')">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                Schedule Exam
                            </button>
                        </form>
                    @endif

                    @if(!$exam->grades()->exists())
                        <form method="POST" action="{{ route('admin.grading.exams.destroy', $exam) }}" class="w-full">
                            @csrf
                            @method('DELETE')
                            <button type="submit"
                                    style="cursor: pointer;"
                                    class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors text-center"
                                    onclick="return confirm('Are you sure you want to delete this exam? This action cannot be undone.')">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                                Delete Exam
                            </button>
                        </form>
                    @endif
                </div>
            </div>

            <!-- Statistics -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Statistics</h3>

                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Total Subjects:</span>
                        <span class="badge badge-blue">{{ $exam->examSubjects->count() }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Target Classes:</span>
                        <span class="badge badge-indigo">{{ $targetClasses->count() }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Grades Recorded:</span>
                        <span class="badge badge-green">{{ $exam->grades->count() }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
