<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class GradeReport extends Model
{
    use HasFactory;

    protected $fillable = [
        'student_id',
        'academic_year_id',
        'academic_term_id',
        'report_type',
        'subject_grades',
        'overall_percentage',
        'overall_gpa',
        'overall_grade',
        'class_rank',
        'total_students',
        'teacher_comments',
        'principal_comments',
        'parent_comments',
        'attendance_days_present',
        'attendance_total_days',
        'attendance_percentage',
        'status',
        'published_at',
        'sent_at',
        'generated_by',
    ];

    protected $casts = [
        'subject_grades' => 'array',
        'overall_percentage' => 'decimal:2',
        'overall_gpa' => 'decimal:2',
        'class_rank' => 'integer',
        'total_students' => 'integer',
        'attendance_days_present' => 'integer',
        'attendance_total_days' => 'integer',
        'attendance_percentage' => 'decimal:2',
        'published_at' => 'datetime',
        'sent_at' => 'datetime',
    ];

    /**
     * Get the student.
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * Get the academic year.
     */
    public function academicYear(): BelongsTo
    {
        return $this->belongsTo(AcademicYear::class);
    }

    /**
     * Get the academic term.
     */
    public function academicTerm(): BelongsTo
    {
        return $this->belongsTo(AcademicTerm::class);
    }

    /**
     * Get the generator.
     */
    public function generator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'generated_by');
    }

    /**
     * Get status badge color.
     */
    public function getStatusBadgeColorAttribute(): string
    {
        return match($this->status) {
            'draft' => 'badge-gray',
            'published' => 'badge-green',
            'sent' => 'badge-blue',
            default => 'badge-gray',
        };
    }

    /**
     * Get formatted overall percentage.
     */
    public function getFormattedOverallPercentageAttribute(): string
    {
        return number_format($this->overall_percentage, 1) . '%';
    }

    /**
     * Get formatted GPA.
     */
    public function getFormattedGpaAttribute(): string
    {
        return number_format($this->overall_gpa, 2);
    }

    /**
     * Get formatted rank.
     */
    public function getFormattedRankAttribute(): string
    {
        if (!$this->class_rank || !$this->total_students) {
            return 'N/A';
        }

        return $this->class_rank . ' of ' . $this->total_students;
    }

    /**
     * Get formatted attendance.
     */
    public function getFormattedAttendanceAttribute(): string
    {
        if (!$this->attendance_total_days) {
            return 'N/A';
        }

        return $this->attendance_days_present . '/' . $this->attendance_total_days . 
               ' (' . number_format($this->attendance_percentage, 1) . '%)';
    }
}
