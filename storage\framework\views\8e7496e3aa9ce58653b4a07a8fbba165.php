<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Page Header -->
    <?php if (isset($component)) { $__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.page-header','data' => ['title' => 'Subject Details','description' => 'View and manage subject information','backRoute' => route('admin.academic.subjects.index'),'backLabel' => 'Back to Subjects']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('page-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Subject Details','description' => 'View and manage subject information','back-route' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.academic.subjects.index')),'back-label' => 'Back to Subjects']); ?>
        
        <div class="flex items-center space-x-3">
            <a href="<?php echo e(route('admin.academic.subjects.edit', $subject)); ?>" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                </svg>
                Edit Subject
            </a>

            <button type="button"
                    onclick="toggleSubjectStatus('<?php echo e($subject->id); ?>', '<?php echo e($subject->name); ?>', <?php echo e($subject->is_active ? 'false' : 'true'); ?>)"
                    class="btn-<?php echo e($subject->is_active ? 'secondary' : 'primary'); ?>">
                <?php if($subject->is_active): ?>
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                    </svg>
                    Deactivate
                <?php else: ?>
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Activate
                <?php endif; ?>
            </button>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e)): ?>
<?php $attributes = $__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e; ?>
<?php unset($__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e)): ?>
<?php $component = $__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e; ?>
<?php unset($__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e); ?>
<?php endif; ?>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Subject Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="card">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
                    <div class="flex items-center space-x-2">
                        <span class="badge badge-<?php echo e($subject->category_badge_color); ?>">
                            <?php echo e($subject->category); ?>

                        </span>
                        <span class="badge badge-<?php echo e($subject->status_badge_color); ?>">
                            <?php echo e($subject->status_display); ?>

                        </span>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Subject Code</dt>
                        <dd class="mt-1 text-sm text-gray-900 font-mono"><?php echo e($subject->subject_code); ?></dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Subject Name</dt>
                        <dd class="mt-1 text-sm text-gray-900"><?php echo e($subject->name); ?></dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Credits</dt>
                        <dd class="mt-1 text-sm text-gray-900"><?php echo e($subject->credits); ?></dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Grade Levels</dt>
                        <dd class="mt-1 text-sm text-gray-900"><?php echo e($subject->formatted_grade_levels); ?></dd>
                    </div>
                </div>

                <?php if($subject->description): ?>
                    <div class="mt-6">
                        <dt class="text-sm font-medium text-gray-500">Description</dt>
                        <dd class="mt-1 text-sm text-gray-900"><?php echo e($subject->description); ?></dd>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Prerequisites -->
            <?php if($prerequisiteSubjects->count() > 0): ?>
                <div class="card">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Prerequisites</h3>
                    <div class="space-y-3">
                        <?php $__currentLoopData = $prerequisiteSubjects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $prerequisite): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <div class="text-sm font-medium text-gray-900"><?php echo e($prerequisite->name); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo e($prerequisite->subject_code); ?></div>
                                </div>
                                <span class="badge badge-<?php echo e($prerequisite->category_badge_color); ?>">
                                    <?php echo e($prerequisite->category); ?>

                                </span>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Dependent Subjects -->
            <?php if($dependentSubjects->count() > 0): ?>
                <div class="card">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Subjects that require this as prerequisite</h3>
                    <div class="space-y-3">
                        <?php $__currentLoopData = $dependentSubjects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $dependent): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <div class="text-sm font-medium text-gray-900"><?php echo e($dependent->name); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo e($dependent->subject_code); ?></div>
                                </div>
                                <span class="badge badge-<?php echo e($dependent->category_badge_color); ?>">
                                    <?php echo e($dependent->category); ?>

                                </span>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Statistics and Assignments -->
        <div class="space-y-6">
            <!-- Statistics -->
            <div class="card">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Statistics</h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">Classes Offering</span>
                        <span class="text-sm font-medium text-gray-900"><?php echo e($subject->classes->count()); ?></span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">Assigned Teachers</span>
                        <span class="text-sm font-medium text-gray-900"><?php echo e($subject->teachers->count()); ?></span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">Prerequisites</span>
                        <span class="text-sm font-medium text-gray-900"><?php echo e($prerequisiteSubjects->count()); ?></span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">Dependent Subjects</span>
                        <span class="text-sm font-medium text-gray-900"><?php echo e($dependentSubjects->count()); ?></span>
                    </div>
                </div>
            </div>

            <!-- Classes Offering This Subject -->
            <?php if($subject->classes->count() > 0): ?>
                <div class="card">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Classes Offering This Subject</h3>
                    <div class="space-y-3">
                        <?php $__currentLoopData = $subject->classes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $class): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <div class="text-sm font-medium text-gray-900"><?php echo e($class->name); ?></div>
                                    <div class="text-sm text-gray-500">
                                        <?php echo e($class->pivot->hours_per_week); ?> hours/week
                                        <?php if($class->pivot->is_mandatory): ?>
                                            • Mandatory
                                        <?php else: ?>
                                            • Elective
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <span class="badge badge-<?php echo e($class->level_badge_color); ?>">
                                    <?php echo e($class->level); ?>

                                </span>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Assigned Teachers -->
            <?php if($subject->teachers->count() > 0): ?>
                <div class="card">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Assigned Teachers</h3>
                    <div class="space-y-3">
                        <?php $__currentLoopData = $subject->teachers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $teacher): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                                        <span class="text-white text-sm font-medium">
                                            <?php echo e(substr($teacher->user->name, 0, 2)); ?>

                                        </span>
                                    </div>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="text-sm font-medium text-gray-900"><?php echo e($teacher->user->name); ?></div>
                                    <div class="text-sm text-gray-500">
                                        <?php if($teacher->pivot->class_id): ?>
                                            Class: <?php echo e($teacher->pivot->class->name ?? 'N/A'); ?>

                                            <?php if($teacher->pivot->section_id): ?>
                                                - <?php echo e($teacher->pivot->section->name ?? 'N/A'); ?>

                                            <?php endif; ?>
                                        <?php endif; ?>
                                        <?php if($teacher->pivot->is_primary): ?>
                                            <span class="badge badge-blue ml-2">Primary</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
// Toggle subject status
async function toggleSubjectStatus(subjectId, subjectName, newStatus) {
    const action = newStatus === 'true' ? 'activate' : 'deactivate';
    
    const confirmed = await confirmAction(
        `${action.charAt(0).toUpperCase() + action.slice(1)} Subject`,
        `Are you sure you want to ${action} "${subjectName}"?`
    );
    
    if (confirmed) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/academic/subjects/${subjectId}/toggle-status`;
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        form.appendChild(csrfToken);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\school-management-system\resources\views/admin/subjects/show.blade.php ENDPATH**/ ?>