<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Student extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'student_id',
        'date_of_birth',
        'gender',
        'class',
        'section',
        'roll_number',
        'admission_date',
        'blood_group',
        'medical_conditions',
        'emergency_contact',
        'guardian_ids',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'admission_date' => 'date',
        'guardian_ids' => 'array',
    ];

    /**
     * Get the user that owns the student profile
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the guardians for this student
     */
    public function guardians()
    {
        return $this->belongsToMany(Guardian::class, 'guardian_student');
    }

    /**
     * Get the invoices for this student
     */
    public function invoices()
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * Get the attendance records for this student
     */
    public function attendances()
    {
        return $this->hasMany(StudentAttendance::class);
    }

    /**
     * Get the current enrollment for this student
     */
    public function currentEnrollment()
    {
        return $this->hasOne(StudentEnrollment::class)->active()->currentYear();
    }

    /**
     * Get all enrollments for this student
     */
    public function enrollments()
    {
        return $this->hasMany(StudentEnrollment::class);
    }

    /**
     * Get the current class through enrollment
     */
    public function currentClass()
    {
        return $this->hasOneThrough(
            ClassModel::class,
            StudentEnrollment::class,
            'student_id',
            'id',
            'id',
            'class_id'
        )->where('student_enrollments.is_active', true);
    }

    /**
     * Get the current section through enrollment
     */
    public function currentSection()
    {
        return $this->hasOneThrough(
            Section::class,
            StudentEnrollment::class,
            'student_id',
            'id',
            'id',
            'section_id'
        )->where('student_enrollments.is_active', true);
    }

    /**
     * Get all subjects for this student (mandatory + elective)
     */
    public function subjects()
    {
        return $this->belongsToMany(Subject::class, 'student_subjects', 'student_id', 'subject_id')
                    ->withPivot('class_id', 'section_id', 'enrollment_date', 'end_date', 'is_active')
                    ->withTimestamps();
    }

    /**
     * Get active subjects for this student
     */
    public function activeSubjects()
    {
        return $this->subjects()->wherePivot('is_active', true);
    }

    /**
     * Get mandatory subjects based on current class
     */
    public function mandatorySubjects()
    {
        $currentEnrollment = $this->currentEnrollment;
        if (!$currentEnrollment) {
            return collect();
        }

        return $currentEnrollment->class->subjects()->wherePivot('is_mandatory', true)->get();
    }

    /**
     * Get teachers for a specific subject
     */
    public function getTeachersForSubject($subjectId)
    {
        $currentEnrollment = $this->currentEnrollment;
        if (!$currentEnrollment) {
            return collect();
        }

        return Teacher::whereHas('teachingAssignments', function($query) use ($subjectId, $currentEnrollment) {
            $query->where('subject_id', $subjectId)
                  ->where('class_id', $currentEnrollment->class_id)
                  ->where(function($q) use ($currentEnrollment) {
                      $q->where('section_id', $currentEnrollment->section_id)
                        ->orWhereNull('section_id');
                  });
        })->with('user')->get();
    }

    /**
     * Get the full name of the student
     */
    public function getFullNameAttribute()
    {
        return $this->user->name;
    }

    /**
     * Get the student's age
     */
    public function getAgeAttribute()
    {
        return $this->date_of_birth->age;
    }

    /**
     * Get the student's class and section combined
     */
    public function getClassSectionAttribute()
    {
        if ($this->class && $this->section) {
            return $this->class . ' - ' . $this->section;
        }
        return $this->class ?: 'Not Assigned';
    }

    /**
     * Check if student is active
     */
    public function getIsActiveAttribute()
    {
        return $this->user->is_active;
    }

    /**
     * Get total amount paid by student
     */
    public function getTotalAmountPaidAttribute()
    {
        return $this->invoices()->where('status', 'paid')->sum('total_amount');
    }

    /**
     * Get total amount due by student
     */
    public function getTotalAmountDueAttribute()
    {
        return $this->invoices()->whereIn('status', ['sent', 'overdue'])->sum('total_amount');
    }

    /**
     * Scope to filter active students
     */
    public function scopeActive($query)
    {
        return $query->whereHas('user', function ($q) {
            $q->where('is_active', true);
        });
    }

    /**
     * Scope to filter by class
     */
    public function scopeByClass($query, $class)
    {
        return $query->where('class', $class);
    }

    /**
     * Scope to filter by section
     */
    public function scopeBySection($query, $section)
    {
        return $query->where('section', $section);
    }

    /**
     * Scope to search students
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->whereHas('user', function ($userQuery) use ($search) {
                $userQuery->where('name', 'like', "%{$search}%")
                          ->orWhere('email', 'like', "%{$search}%")
                          ->orWhere('phone', 'like', "%{$search}%")
                          ->orWhere('address', 'like', "%{$search}%");
            })->orWhere('student_id', 'like', "%{$search}%")
              ->orWhere('class', 'like', "%{$search}%")
              ->orWhere('section', 'like', "%{$search}%")
              ->orWhere('roll_number', 'like', "%{$search}%")
              ->orWhere('blood_group', 'like', "%{$search}%")
              ->orWhere('medical_conditions', 'like', "%{$search}%")
              ->orWhere('emergency_contact', 'like', "%{$search}%")
              ->orWhere('gender', 'like', "%{$search}%")
              ->orWhereHas('guardians.user', function ($guardianQuery) use ($search) {
                  $guardianQuery->where('name', 'like', "%{$search}%");
              });
        });
    }
}
