<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Carbon\Carbon;

class Exam extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'exam_code',
        'description',
        'academic_year_id',
        'academic_term_id',
        'grade_category_id',
        'grade_scale_id',
        'exam_date',
        'start_time',
        'end_time',
        'duration_minutes',
        'total_marks',
        'passing_marks',
        'status',
        'instructions',
        'target_classes',
        'created_by',
        'is_published',
        'published_at',
    ];

    protected $casts = [
        'exam_date' => 'date',
        'start_time' => 'datetime:H:i',
        'end_time' => 'datetime:H:i',
        'total_marks' => 'decimal:2',
        'passing_marks' => 'decimal:2',
        'target_classes' => 'array',
        'is_published' => 'boolean',
        'published_at' => 'datetime',
        'duration_minutes' => 'integer',
    ];

    /**
     * Get the academic year.
     */
    public function academicYear(): BelongsTo
    {
        return $this->belongsTo(AcademicYear::class);
    }

    /**
     * Get the academic term.
     */
    public function academicTerm(): BelongsTo
    {
        return $this->belongsTo(AcademicTerm::class);
    }

    /**
     * Get the grade category.
     */
    public function gradeCategory(): BelongsTo
    {
        return $this->belongsTo(GradeCategory::class);
    }

    /**
     * Get the grade scale.
     */
    public function gradeScale(): BelongsTo
    {
        return $this->belongsTo(GradeScale::class);
    }

    /**
     * Get the creator.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the exam subjects.
     */
    public function examSubjects(): HasMany
    {
        return $this->hasMany(ExamSubject::class);
    }

    /**
     * Get the grades for this exam.
     */
    public function grades(): MorphMany
    {
        return $this->morphMany(Grade::class, 'gradeable');
    }

    /**
     * Scope for published exams.
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    /**
     * Scope for current academic year.
     */
    public function scopeCurrentYear($query)
    {
        $currentYear = AcademicYear::current();
        if ($currentYear) {
            return $query->where('academic_year_id', $currentYear->id);
        }
        return $query;
    }

    /**
     * Scope for current term.
     */
    public function scopeCurrentTerm($query)
    {
        $currentTerm = AcademicTerm::current();
        if ($currentTerm) {
            return $query->where('academic_term_id', $currentTerm->id);
        }
        return $query;
    }

    /**
     * Get status badge color.
     */
    public function getStatusBadgeColorAttribute(): string
    {
        return match($this->status) {
            'draft' => 'badge-gray',
            'scheduled' => 'badge-blue',
            'ongoing' => 'badge-yellow',
            'completed' => 'badge-green',
            'cancelled' => 'badge-red',
            default => 'badge-gray',
        };
    }

    /**
     * Get formatted exam date.
     */
    public function getFormattedDateAttribute(): string
    {
        return $this->exam_date->format('M d, Y');
    }

    /**
     * Get formatted time range.
     */
    public function getFormattedTimeAttribute(): string
    {
        if ($this->start_time && $this->end_time) {
            return $this->start_time->format('g:i A') . ' - ' . $this->end_time->format('g:i A');
        }
        return 'Time not set';
    }

    /**
     * Check if exam is upcoming.
     */
    public function getIsUpcomingAttribute(): bool
    {
        return $this->exam_date->isFuture();
    }

    /**
     * Check if exam is today.
     */
    public function getIsTodayAttribute(): bool
    {
        return $this->exam_date->isToday();
    }

    /**
     * Get passing percentage.
     */
    public function getPassingPercentageAttribute(): float
    {
        return ($this->passing_marks / $this->total_marks) * 100;
    }
}
