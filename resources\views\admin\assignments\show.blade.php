@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <x-page-header
        title="Assignment Details"
        description="View assignment information and submissions"
        :back-route="route('admin.grading.assignments.index')"
        back-label="Back to Assignments">
        
        <div class="flex items-center space-x-3">
            <a href="{{ route('admin.grading.assignments.edit', $assignment) }}" class="btn-secondary" style="cursor: pointer;">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                </svg>
                Edit Assignment
            </a>

            <form method="POST" action="{{ route('admin.grading.assignments.toggle-status', $assignment) }}" class="inline toggle-assignment-status-form">
                @csrf
                <button type="button"
                        class="btn-{{ $assignment->status === 'active' ? 'warning' : 'success' }} toggle-assignment-status-btn"
                        style="cursor: pointer;"
                        data-assignment-id="{{ $assignment->id }}"
                        data-assignment-title="{{ $assignment->title }}"
                        data-current-status="{{ $assignment->status }}">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                    </svg>
                    {{ $assignment->status === 'active' ? 'Set to Draft' : 'Activate' }}
                </button>
            </form>
        </div>
    </x-page-header>

    <!-- Assignment Information -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 bg-gradient-to-r from-blue-500 to-blue-600">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-white">{{ $assignment->title }}</h1>
                    <p class="text-blue-100">{{ $assignment->assignment_code }}</p>
                </div>
                <div class="flex items-center space-x-3">
                    <span class="badge {{ $assignment->status === 'active' ? 'badge-green' : 'badge-yellow' }}">
                        {{ ucfirst($assignment->status) }}
                    </span>
                    @if($assignment->is_published)
                        <span class="badge badge-blue">Published</span>
                    @else
                        <span class="badge badge-gray">Draft</span>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Submission Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-blue-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Total Students</dt>
                            <dd class="stat-card-value">{{ $submissionStats['total_students'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-green-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Submitted</dt>
                            <dd class="stat-card-value">{{ $submissionStats['submitted'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-yellow-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Pending</dt>
                            <dd class="stat-card-value">{{ $submissionStats['pending'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-purple-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Graded</dt>
                            <dd class="stat-card-value">{{ $submissionStats['graded'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Assignment Details -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Assignment Information</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Title</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $assignment->title }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Assignment Code</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $assignment->assignment_code }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Subject</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $assignment->subject->name }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Class</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $assignment->classRoom->name }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Teacher</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $assignment->teacher ? $assignment->teacher->name : 'Not assigned' }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Grade Category</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $assignment->gradeCategory->name }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Assigned Date</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $assignment->assigned_date->format('M d, Y') }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Due Date</label>
                        <p class="mt-1 text-sm text-gray-900">
                            {{ $assignment->due_date->format('M d, Y') }}
                            @if($assignment->due_time)
                                at {{ $assignment->due_time->format('H:i') }}
                            @endif
                        </p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Total Marks</label>
                        <p class="mt-1 text-sm text-gray-900">{{ number_format($assignment->total_marks, 1) }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Passing Marks</label>
                        <p class="mt-1 text-sm text-gray-900">{{ number_format($assignment->passing_marks, 1) }}</p>
                    </div>
                    
                    @if($assignment->description)
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">Description</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $assignment->description }}</p>
                        </div>
                    @endif
                    
                    @if($assignment->instructions)
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">Instructions</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $assignment->instructions }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Recent Submissions -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Recent Submissions</h3>
                        <a href="{{ route('admin.grading.assignments.submissions', $assignment) }}" class="text-sm text-blue-600 hover:text-blue-500" style="cursor: pointer;">
                            View all submissions
                        </a>
                    </div>
                </div>

                @if($assignment->submissions->count() > 0)
                    <div class="divide-y divide-gray-200">
                        @foreach($assignment->submissions->take(10) as $submission)
                            <div class="px-6 py-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">{{ $submission->student->user->name }}</p>
                                        <p class="text-sm text-gray-500">
                                            Submitted: {{ $submission->submitted_at->format('M d, Y H:i') }}
                                        </p>
                                    </div>
                                    <div class="text-right">
                                        @if($submission->grade && $submission->grade->marks_obtained !== null)
                                            <p class="text-sm font-medium text-gray-900">
                                                {{ number_format($submission->grade->marks_obtained, 1) }}/{{ number_format($assignment->total_marks, 1) }}
                                            </p>
                                            <span class="badge badge-green">Graded</span>
                                        @else
                                            <span class="badge badge-yellow">Pending Review</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="px-6 py-8 text-center">
                        <p class="text-sm text-gray-500">No submissions yet.</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Assignment Settings -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Assignment Settings</h3>
                
                <div class="space-y-4">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Submission Type:</span>
                        <span class="text-sm font-medium text-gray-900">{{ ucfirst($assignment->submission_type) }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Late Submission:</span>
                        <span class="text-sm font-medium text-gray-900">{{ $assignment->allow_late_submission ? 'Allowed' : 'Not Allowed' }}</span>
                    </div>
                    @if($assignment->allow_late_submission && $assignment->late_penalty_percentage)
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Late Penalty:</span>
                            <span class="text-sm font-medium text-gray-900">{{ $assignment->late_penalty_percentage }}%</span>
                        </div>
                    @endif
                    @if($assignment->max_attempts)
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Max Attempts:</span>
                            <span class="text-sm font-medium text-gray-900">{{ $assignment->max_attempts }}</span>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Performance Summary -->
            @if($submissionStats['graded'] > 0)
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Performance Summary</h3>
                    
                    <div class="space-y-4">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Average Score:</span>
                            <span class="text-sm font-medium text-gray-900">
                                {{ number_format($submissionStats['average_marks'], 1) }}/{{ number_format($assignment->total_marks, 1) }}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Completion Rate:</span>
                            <span class="text-sm font-medium text-gray-900">
                                {{ number_format(($submissionStats['submitted'] / $submissionStats['total_students']) * 100, 1) }}%
                            </span>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Quick Actions -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                
                <div class="space-y-3">
                    <a href="{{ route('admin.grading.assignments.edit', $assignment) }}"
                       class="w-full btn-primary text-center" style="cursor: pointer;">
                        Edit Assignment
                    </a>

                    <a href="{{ route('admin.grading.assignments.submissions', $assignment) }}"
                       class="w-full btn-secondary text-center" style="cursor: pointer;">
                        View Submissions
                    </a>

                    <form method="POST" action="{{ route('admin.grading.assignments.toggle-status', $assignment) }}" class="w-full toggle-assignment-status-form">
                        @csrf
                        <button type="button"
                                class="w-full btn-{{ $assignment->status === 'active' ? 'warning' : 'success' }} text-center toggle-assignment-status-btn"
                                style="cursor: pointer;"
                                data-assignment-id="{{ $assignment->id }}"
                                data-assignment-title="{{ $assignment->title }}"
                                data-current-status="{{ $assignment->status }}">
                            {{ $assignment->status === 'active' ? 'Set to Draft' : 'Activate' }}
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Handle toggle assignment status with custom modal confirmation
document.addEventListener('DOMContentLoaded', function() {
    const toggleButtons = document.querySelectorAll('.toggle-assignment-status-btn');

    toggleButtons.forEach(button => {
        button.addEventListener('click', async function(e) {
            e.preventDefault();

            const assignmentId = this.getAttribute('data-assignment-id');
            const assignmentTitle = this.getAttribute('data-assignment-title');
            const currentStatus = this.getAttribute('data-current-status');

            const action = currentStatus === 'active' ? 'set to draft' : 'activate';
            const actionText = currentStatus === 'active' ? 'Set to Draft' : 'Activate';

            const confirmed = await confirmModal({
                title: `${actionText} Assignment`,
                message: `Are you sure you want to ${action} the assignment "${assignmentTitle}"?`,
                confirmText: actionText,
                cancelText: 'Cancel',
                type: currentStatus === 'active' ? 'warning' : 'success'
            });

            if (confirmed) {
                // Submit the form
                const form = this.closest('.toggle-assignment-status-form');
                form.submit();
            }
        });
    });
});
</script>
@endpush
