<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\GradeScale;
use App\Models\GradeCategory;
use App\Models\GradeCommentTemplate;
use App\Models\Exam;
use App\Models\ExamSubject;
use App\Models\Assignment;
use App\Models\AssignmentSubmission;
use App\Models\Grade;
use App\Models\GradeReport;
use App\Models\AcademicYear;
use App\Models\AcademicTerm;
use App\Models\Subject;
use App\Models\Student;
use App\Models\User;
use App\Models\ClassModel;
use App\Models\Section;
use Carbon\Carbon;

class GradingSystemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Grade Scales
        $this->createGradeScales();

        // Create Grade Categories
        $this->createGradeCategories();

        // Create Grade Comment Templates
        $this->createGradeCommentTemplates();

        // Create Sample Assessment Data
        $this->createSampleAssessmentData();
    }

    private function createGradeScales(): void
    {
        // A-F Letter Grade Scale
        GradeScale::create([
            'name' => 'A-F Letter Grade Scale',
            'type' => 'letter',
            'scale_values' => [
                ['min' => 97, 'max' => 100, 'grade' => 'A+', 'points' => 4.0],
                ['min' => 93, 'max' => 96, 'grade' => 'A', 'points' => 4.0],
                ['min' => 90, 'max' => 92, 'grade' => 'A-', 'points' => 3.7],
                ['min' => 87, 'max' => 89, 'grade' => 'B+', 'points' => 3.3],
                ['min' => 83, 'max' => 86, 'grade' => 'B', 'points' => 3.0],
                ['min' => 80, 'max' => 82, 'grade' => 'B-', 'points' => 2.7],
                ['min' => 77, 'max' => 79, 'grade' => 'C+', 'points' => 2.3],
                ['min' => 73, 'max' => 76, 'grade' => 'C', 'points' => 2.0],
                ['min' => 70, 'max' => 72, 'grade' => 'C-', 'points' => 1.7],
                ['min' => 67, 'max' => 69, 'grade' => 'D+', 'points' => 1.3],
                ['min' => 63, 'max' => 66, 'grade' => 'D', 'points' => 1.0],
                ['min' => 60, 'max' => 62, 'grade' => 'D-', 'points' => 0.7],
                ['min' => 0, 'max' => 59, 'grade' => 'F', 'points' => 0.0],
            ],
            'is_default' => true,
            'is_active' => true,
        ]);

        // Percentage Scale
        GradeScale::create([
            'name' => 'Percentage Scale',
            'type' => 'percentage',
            'scale_values' => [
                ['min' => 90, 'max' => 100, 'grade' => 'Excellent', 'points' => 4.0],
                ['min' => 80, 'max' => 89, 'grade' => 'Very Good', 'points' => 3.0],
                ['min' => 70, 'max' => 79, 'grade' => 'Good', 'points' => 2.0],
                ['min' => 60, 'max' => 69, 'grade' => 'Satisfactory', 'points' => 1.0],
                ['min' => 0, 'max' => 59, 'grade' => 'Needs Improvement', 'points' => 0.0],
            ],
            'is_default' => false,
            'is_active' => true,
        ]);

        // Malaysian UPSR Scale
        GradeScale::create([
            'name' => 'Malaysian UPSR Scale',
            'type' => 'letter',
            'scale_values' => [
                ['min' => 90, 'max' => 100, 'grade' => 'A', 'points' => 4.0],
                ['min' => 80, 'max' => 89, 'grade' => 'B', 'points' => 3.0],
                ['min' => 65, 'max' => 79, 'grade' => 'C', 'points' => 2.0],
                ['min' => 50, 'max' => 64, 'grade' => 'D', 'points' => 1.0],
                ['min' => 0, 'max' => 49, 'grade' => 'E', 'points' => 0.0],
            ],
            'is_default' => false,
            'is_active' => true,
        ]);
    }

    private function createGradeCategories(): void
    {
        $categories = [
            [
                'name' => 'Quiz',
                'code' => 'QUIZ',
                'description' => 'Short assessments and pop quizzes',
                'weight_percentage' => 15.0,
                'color' => '#3b82f6',
                'sort_order' => 1,
            ],
            [
                'name' => 'Assignment',
                'code' => 'ASSIGN',
                'description' => 'Homework and take-home assignments',
                'weight_percentage' => 20.0,
                'color' => '#8b5cf6',
                'sort_order' => 2,
            ],
            [
                'name' => 'Test',
                'code' => 'TEST',
                'description' => 'Unit tests and chapter tests',
                'weight_percentage' => 25.0,
                'color' => '#f59e0b',
                'sort_order' => 3,
            ],
            [
                'name' => 'Project',
                'code' => 'PROJECT',
                'description' => 'Long-term projects and presentations',
                'weight_percentage' => 15.0,
                'color' => '#06b6d4',
                'sort_order' => 4,
            ],
            [
                'name' => 'Midterm Exam',
                'code' => 'MIDTERM',
                'description' => 'Mid-semester examinations',
                'weight_percentage' => 10.0,
                'color' => '#f97316',
                'sort_order' => 5,
            ],
            [
                'name' => 'Final Exam',
                'code' => 'FINAL',
                'description' => 'End-of-term final examinations',
                'weight_percentage' => 15.0,
                'color' => '#ef4444',
                'sort_order' => 6,
            ],
        ];

        foreach ($categories as $category) {
            GradeCategory::create($category);
        }
    }

    private function createGradeCommentTemplates(): void
    {
        $templates = [
            // Excellent Performance
            [
                'title' => 'Outstanding Achievement',
                'comment' => 'Demonstrates exceptional understanding and consistently produces high-quality work.',
                'category' => 'excellent',
                'grade_range' => ['min' => 95, 'max' => 100],
                'created_by' => 1,
                'is_public' => true,
            ],
            [
                'title' => 'Excellent Work',
                'comment' => 'Shows excellent grasp of concepts and applies knowledge effectively.',
                'category' => 'excellent',
                'grade_range' => ['min' => 90, 'max' => 94],
                'created_by' => 1,
                'is_public' => true,
            ],
            
            // Positive Performance
            [
                'title' => 'Good Progress',
                'comment' => 'Making good progress and showing solid understanding of the material.',
                'category' => 'positive',
                'grade_range' => ['min' => 80, 'max' => 89],
                'created_by' => 1,
                'is_public' => true,
            ],
            [
                'title' => 'Satisfactory Work',
                'comment' => 'Demonstrates satisfactory understanding with room for growth.',
                'category' => 'positive',
                'grade_range' => ['min' => 70, 'max' => 79],
                'created_by' => 1,
                'is_public' => true,
            ],
            
            // Needs Improvement
            [
                'title' => 'Needs Additional Support',
                'comment' => 'Would benefit from additional practice and support to strengthen understanding.',
                'category' => 'improvement',
                'grade_range' => ['min' => 60, 'max' => 69],
                'created_by' => 1,
                'is_public' => true,
            ],
            [
                'title' => 'Requires Intervention',
                'comment' => 'Requires immediate intervention and additional support to meet learning objectives.',
                'category' => 'concern',
                'grade_range' => ['min' => 0, 'max' => 59],
                'created_by' => 1,
                'is_public' => true,
            ],
            
            // General Comments
            [
                'title' => 'Active Participation',
                'comment' => 'Shows excellent participation and engagement in class activities.',
                'category' => 'positive',
                'grade_range' => null,
                'created_by' => 1,
                'is_public' => true,
            ],
            [
                'title' => 'Consistent Effort',
                'comment' => 'Demonstrates consistent effort and dedication to learning.',
                'category' => 'positive',
                'grade_range' => null,
                'created_by' => 1,
                'is_public' => true,
            ],
        ];

        foreach ($templates as $template) {
            GradeCommentTemplate::create($template);
        }
    }

    private function createSampleAssessmentData(): void
    {
        // Get required data
        $academicYear = AcademicYear::where('is_current', true)->first();
        $currentTerm = AcademicTerm::where('is_current', true)->first();
        $gradeScale = GradeScale::where('is_default', true)->first();
        $students = Student::with('user')->get();
        $teachers = User::where('user_type', 'teacher')->get();
        $subjects = Subject::whereIn('subject_code', ['ENG001', 'MATH001', 'SCI001', 'PHY001', 'CHEM001'])->get();
        $gradeCategories = GradeCategory::all();

        if (!$academicYear || !$currentTerm || !$gradeScale || $students->isEmpty() || $teachers->isEmpty() || $subjects->isEmpty()) {
            return; // Skip if required data is missing
        }

        // Create sample exams
        $this->createSampleExams($academicYear, $currentTerm, $gradeScale, $subjects, $teachers);

        // Create sample assignments
        $this->createSampleAssignments($academicYear, $currentTerm, $gradeScale, $subjects, $teachers);

        // Create sample grades
        $this->createSampleGrades($academicYear, $currentTerm, $gradeScale, $students, $subjects, $teachers, $gradeCategories);

        // Create sample grade reports
        $this->createSampleGradeReports($academicYear, $currentTerm, $students);
    }

    private function createSampleExams($academicYear, $currentTerm, $gradeScale, $subjects, $teachers): void
    {
        $examCategory = GradeCategory::where('code', 'TEST')->first();
        $finalCategory = GradeCategory::where('code', 'FINAL')->first();

        if (!$examCategory || !$finalCategory) return;

        // Create Mid-Term Exam
        $midTermExam = Exam::create([
            'title' => 'Mid-Term Examination',
            'exam_code' => 'MTE-' . $currentTerm->id . '-2024',
            'description' => 'Mid-term examination for ' . $currentTerm->name,
            'academic_year_id' => $academicYear->id,
            'academic_term_id' => $currentTerm->id,
            'grade_category_id' => $examCategory->id,
            'grade_scale_id' => $gradeScale->id,
            'exam_date' => Carbon::now()->subDays(15),
            'start_time' => '08:00',
            'end_time' => '12:00',
            'duration_minutes' => 240,
            'total_marks' => 100,
            'passing_marks' => 50,
            'status' => 'completed',
            'instructions' => 'Please read all instructions carefully. Answer all questions.',
            'target_classes' => ['Form 3', 'Form 4'],
            'created_by' => $teachers->first()->id,
            'is_published' => true,
            'published_at' => Carbon::now()->subDays(5),
        ]);

        // Create Final Exam
        $finalExam = Exam::create([
            'title' => 'Final Examination',
            'exam_code' => 'FE-' . $currentTerm->id . '-2024',
            'description' => 'Final examination for ' . $currentTerm->name,
            'academic_year_id' => $academicYear->id,
            'academic_term_id' => $currentTerm->id,
            'grade_category_id' => $finalCategory->id,
            'grade_scale_id' => $gradeScale->id,
            'exam_date' => Carbon::now()->addDays(30),
            'start_time' => '08:00',
            'end_time' => '15:00',
            'duration_minutes' => 420,
            'total_marks' => 100,
            'passing_marks' => 50,
            'status' => 'scheduled',
            'instructions' => 'Final examination covering all topics from the term.',
            'target_classes' => ['Form 3', 'Form 4'],
            'created_by' => $teachers->first()->id,
            'is_published' => false,
        ]);

        // Create exam subjects for both exams
        foreach ([$midTermExam, $finalExam] as $exam) {
            foreach ($subjects->take(4) as $index => $subject) {
                $teacher = $teachers->get($index % $teachers->count());

                ExamSubject::create([
                    'exam_id' => $exam->id,
                    'subject_id' => $subject->id,
                    'teacher_id' => $teacher->id,
                    'subject_total_marks' => 100,
                    'subject_passing_marks' => 50,
                    'subject_start_time' => Carbon::createFromFormat('H:i', '08:00')->addHours($index)->format('H:i'),
                    'subject_end_time' => Carbon::createFromFormat('H:i', '08:00')->addHours($index + 2)->format('H:i'),
                    'subject_duration_minutes' => 120,
                    'subject_instructions' => 'Answer all questions for ' . $subject->name,
                ]);
            }
        }
    }

    private function createSampleAssignments($academicYear, $currentTerm, $gradeScale, $subjects, $teachers): void
    {
        $assignmentCategory = GradeCategory::where('code', 'ASSIGN')->first();
        $projectCategory = GradeCategory::where('code', 'PROJECT')->first();

        if (!$assignmentCategory || !$projectCategory) return;

        // Get classes and sections
        $classes = ClassModel::whereIn('name', ['Form 3', 'Form 4'])->get();
        $sections = Section::take(2)->get();

        if ($classes->isEmpty()) return;

        $assignmentData = [
            [
                'title' => 'Essay Writing Assignment',
                'subject_code' => 'ENG001',
                'category' => $assignmentCategory,
                'description' => 'Write a 500-word essay on "The Importance of Education"',
                'total_marks' => 50,
                'due_days_ago' => 10,
                'status' => 'completed',
            ],
            [
                'title' => 'Mathematics Problem Set',
                'subject_code' => 'MATH001',
                'category' => $assignmentCategory,
                'description' => 'Complete exercises 1-20 from Chapter 5',
                'total_marks' => 40,
                'due_days_ago' => 7,
                'status' => 'completed',
            ],
            [
                'title' => 'Science Experiment Report',
                'subject_code' => 'SCI001',
                'category' => $projectCategory,
                'description' => 'Conduct and report on a simple chemistry experiment',
                'total_marks' => 75,
                'due_days_ago' => 3,
                'status' => 'assigned',
            ],
            [
                'title' => 'Physics Lab Report',
                'subject_code' => 'PHY001',
                'category' => $assignmentCategory,
                'description' => 'Write a lab report on pendulum motion experiment',
                'total_marks' => 60,
                'due_days_ago' => -5, // Due in 5 days
                'status' => 'assigned',
            ],
        ];

        foreach ($assignmentData as $index => $data) {
            $subject = $subjects->where('subject_code', $data['subject_code'])->first();
            if (!$subject) continue;

            $teacher = $teachers->get($index % $teachers->count());
            $class = $classes->get($index % $classes->count());
            $section = $sections->isNotEmpty() ? $sections->first() : null;

            $assignment = Assignment::create([
                'title' => $data['title'],
                'assignment_code' => 'ASG-' . strtoupper(substr($data['subject_code'], 0, 3)) . '-' . ($index + 1),
                'description' => $data['description'],
                'academic_year_id' => $academicYear->id,
                'academic_term_id' => $currentTerm->id,
                'subject_id' => $subject->id,
                'class_id' => $class->id,
                'section_id' => $section?->id,
                'teacher_id' => $teacher->id,
                'grade_category_id' => $data['category']->id,
                'grade_scale_id' => $gradeScale->id,
                'assigned_date' => Carbon::now()->subDays(abs($data['due_days_ago']) + 7),
                'due_date' => Carbon::now()->subDays($data['due_days_ago']),
                'due_time' => '23:59',
                'total_marks' => $data['total_marks'],
                'instructions' => $data['description'],
                'allow_late_submission' => true,
                'late_penalty_percentage' => 10,
                'status' => $data['status'],
                'is_published' => true,
            ]);

            // Create submissions for completed assignments
            if ($data['status'] === 'completed') {
                $students = Student::take(2)->get(); // Get first 2 students
                foreach ($students as $student) {
                    AssignmentSubmission::create([
                        'assignment_id' => $assignment->id,
                        'student_id' => $student->id,
                        'submission_text' => 'Sample submission for ' . $data['title'],
                        'submitted_at' => Carbon::now()->subDays($data['due_days_ago'] - 2),
                        'is_late' => false,
                        'teacher_feedback' => 'Good work! Keep it up.',
                        'status' => 'graded',
                    ]);
                }
            }
        }
    }

    private function createSampleGrades($academicYear, $currentTerm, $gradeScale, $students, $subjects, $teachers, $gradeCategories): void
    {
        // Sample grade data for different assessment types
        $gradeData = [
            // Student 1 (Aisha Rahman) - Good performer
            [
                'student_index' => 0,
                'grades' => [
                    ['subject_code' => 'ENG001', 'category_code' => 'QUIZ', 'marks' => 85, 'total' => 100],
                    ['subject_code' => 'ENG001', 'category_code' => 'ASSIGN', 'marks' => 42, 'total' => 50],
                    ['subject_code' => 'ENG001', 'category_code' => 'TEST', 'marks' => 78, 'total' => 100],
                    ['subject_code' => 'MATH001', 'category_code' => 'QUIZ', 'marks' => 92, 'total' => 100],
                    ['subject_code' => 'MATH001', 'category_code' => 'ASSIGN', 'marks' => 36, 'total' => 40],
                    ['subject_code' => 'MATH001', 'category_code' => 'TEST', 'marks' => 88, 'total' => 100],
                    ['subject_code' => 'SCI001', 'category_code' => 'QUIZ', 'marks' => 80, 'total' => 100],
                    ['subject_code' => 'SCI001', 'category_code' => 'PROJECT', 'marks' => 65, 'total' => 75],
                    ['subject_code' => 'PHY001', 'category_code' => 'QUIZ', 'marks' => 75, 'total' => 100],
                    ['subject_code' => 'PHY001', 'category_code' => 'ASSIGN', 'marks' => 52, 'total' => 60],
                ]
            ],
            // Student 2 (Marcus Lim) - Average performer
            [
                'student_index' => 1,
                'grades' => [
                    ['subject_code' => 'ENG001', 'category_code' => 'QUIZ', 'marks' => 72, 'total' => 100],
                    ['subject_code' => 'ENG001', 'category_code' => 'ASSIGN', 'marks' => 38, 'total' => 50],
                    ['subject_code' => 'ENG001', 'category_code' => 'TEST', 'marks' => 68, 'total' => 100],
                    ['subject_code' => 'MATH001', 'category_code' => 'QUIZ', 'marks' => 78, 'total' => 100],
                    ['subject_code' => 'MATH001', 'category_code' => 'ASSIGN', 'marks' => 32, 'total' => 40],
                    ['subject_code' => 'MATH001', 'category_code' => 'TEST', 'marks' => 74, 'total' => 100],
                    ['subject_code' => 'SCI001', 'category_code' => 'QUIZ', 'marks' => 70, 'total' => 100],
                    ['subject_code' => 'SCI001', 'category_code' => 'PROJECT', 'marks' => 58, 'total' => 75],
                    ['subject_code' => 'PHY001', 'category_code' => 'QUIZ', 'marks' => 65, 'total' => 100],
                    ['subject_code' => 'PHY001', 'category_code' => 'ASSIGN', 'marks' => 45, 'total' => 60],
                ]
            ],
        ];

        foreach ($gradeData as $studentData) {
            if (!isset($students[$studentData['student_index']])) continue;

            $student = $students[$studentData['student_index']];

            foreach ($studentData['grades'] as $gradeInfo) {
                $subject = $subjects->where('subject_code', $gradeInfo['subject_code'])->first();
                $category = $gradeCategories->where('code', $gradeInfo['category_code'])->first();

                if (!$subject || !$category) continue;

                $teacher = $teachers->random();
                $percentage = ($gradeInfo['marks'] / $gradeInfo['total']) * 100;

                // Calculate grade letter based on percentage
                $gradeLetter = $this->calculateGradeLetter($percentage);
                $gradePoint = $this->calculateGradePoint($percentage);

                Grade::create([
                    'student_id' => $student->id,
                    'academic_year_id' => $academicYear->id,
                    'academic_term_id' => $currentTerm->id,
                    'subject_id' => $subject->id,
                    'grade_category_id' => $category->id,
                    'grade_scale_id' => $gradeScale->id,
                    'teacher_id' => $teacher->id,
                    'recorded_by' => $teacher->id,
                    'grade_type' => strtolower($gradeInfo['category_code']) === 'assign' ? 'assignment' : strtolower($gradeInfo['category_code']),
                    'marks_obtained' => $gradeInfo['marks'],
                    'total_marks' => $gradeInfo['total'],
                    'percentage' => round($percentage, 2),
                    'grade_letter' => $gradeLetter,
                    'grade_point' => $gradePoint,
                    'is_passed' => $percentage >= 50,
                    'is_published' => true,
                    'status' => 'published',
                    'teacher_comments' => $this->getRandomComment($percentage),
                    'graded_at' => Carbon::now()->subDays(rand(1, 10)),
                    'published_at' => Carbon::now()->subDays(rand(1, 5)),
                ]);
            }
        }
    }

    private function createSampleGradeReports($academicYear, $currentTerm, $students): void
    {
        foreach ($students->take(2) as $index => $student) {
            // Calculate overall performance
            $grades = Grade::where('student_id', $student->id)
                          ->where('academic_term_id', $currentTerm->id)
                          ->get();

            if ($grades->isEmpty()) continue;

            $totalPercentage = $grades->avg('percentage');
            $overallGrade = $this->calculateGradeLetter($totalPercentage);
            $overallGPA = $this->calculateGradePoint($totalPercentage);

            // Group grades by subject
            $subjectGrades = $grades->groupBy('subject_id')->map(function ($subjectGrades) {
                return [
                    'subject_name' => $subjectGrades->first()->subject->name ?? 'Unknown',
                    'average_percentage' => round($subjectGrades->avg('percentage'), 2),
                    'grade_letter' => $this->calculateGradeLetter($subjectGrades->avg('percentage')),
                    'assessments' => $subjectGrades->map(function ($grade) {
                        return [
                            'type' => $grade->grade_type,
                            'marks' => $grade->marks_obtained,
                            'total' => $grade->total_marks,
                            'percentage' => $grade->percentage,
                            'grade' => $grade->grade_letter,
                        ];
                    })->toArray()
                ];
            })->toArray();

            GradeReport::create([
                'student_id' => $student->id,
                'academic_year_id' => $academicYear->id,
                'academic_term_id' => $currentTerm->id,
                'report_type' => 'term_report',
                'subject_grades' => $subjectGrades,
                'overall_percentage' => round($totalPercentage, 2),
                'overall_gpa' => $overallGPA,
                'overall_grade' => $overallGrade,
                'class_rank' => $index + 1,
                'total_students' => $students->count(),
                'teacher_comments' => $this->getDetailedComment($totalPercentage),
                'principal_comments' => 'Keep up the good work and continue to strive for excellence.',
                'attendance_days_present' => 85,
                'attendance_total_days' => 90,
                'attendance_percentage' => 94.44,
                'status' => 'published',
                'published_at' => Carbon::now()->subDays(2),
                'generated_by' => 1,
            ]);
        }
    }

    private function calculateGradeLetter($percentage): string
    {
        if ($percentage >= 97) return 'A+';
        if ($percentage >= 93) return 'A';
        if ($percentage >= 90) return 'A-';
        if ($percentage >= 87) return 'B+';
        if ($percentage >= 83) return 'B';
        if ($percentage >= 80) return 'B-';
        if ($percentage >= 77) return 'C+';
        if ($percentage >= 73) return 'C';
        if ($percentage >= 70) return 'C-';
        if ($percentage >= 67) return 'D+';
        if ($percentage >= 63) return 'D';
        if ($percentage >= 60) return 'D-';
        return 'F';
    }

    private function calculateGradePoint($percentage): float
    {
        if ($percentage >= 97) return 4.0;
        if ($percentage >= 93) return 4.0;
        if ($percentage >= 90) return 3.7;
        if ($percentage >= 87) return 3.3;
        if ($percentage >= 83) return 3.0;
        if ($percentage >= 80) return 2.7;
        if ($percentage >= 77) return 2.3;
        if ($percentage >= 73) return 2.0;
        if ($percentage >= 70) return 1.7;
        if ($percentage >= 67) return 1.3;
        if ($percentage >= 63) return 1.0;
        if ($percentage >= 60) return 0.7;
        return 0.0;
    }

    private function getRandomComment($percentage): string
    {
        if ($percentage >= 90) {
            $comments = [
                'Excellent work! Outstanding performance.',
                'Demonstrates exceptional understanding.',
                'Consistently produces high-quality work.',
                'Shows excellent grasp of concepts.'
            ];
        } elseif ($percentage >= 80) {
            $comments = [
                'Good progress and solid understanding.',
                'Making good progress in the subject.',
                'Shows good grasp of the material.',
                'Consistent effort and good results.'
            ];
        } elseif ($percentage >= 70) {
            $comments = [
                'Satisfactory work with room for improvement.',
                'Shows understanding but needs more practice.',
                'Good effort, keep working hard.',
                'Making steady progress.'
            ];
        } elseif ($percentage >= 60) {
            $comments = [
                'Needs additional support and practice.',
                'Would benefit from extra help.',
                'Shows potential but needs more effort.',
                'Requires more focus and practice.'
            ];
        } else {
            $comments = [
                'Requires immediate intervention and support.',
                'Needs significant improvement.',
                'Must put in more effort to succeed.',
                'Requires additional tutoring and support.'
            ];
        }

        return $comments[array_rand($comments)];
    }

    private function getDetailedComment($percentage): string
    {
        if ($percentage >= 85) {
            return 'Excellent academic performance throughout the term. Shows strong understanding of concepts and consistently produces high-quality work. Continue to maintain this excellent standard.';
        } elseif ($percentage >= 75) {
            return 'Good academic performance with solid understanding of most concepts. Shows consistent effort and engagement. With continued focus, can achieve even better results.';
        } elseif ($percentage >= 65) {
            return 'Satisfactory performance with room for improvement. Shows understanding of basic concepts but needs to work on application and problem-solving skills. Encourage more practice and study.';
        } else {
            return 'Performance below expectations. Requires additional support and intervention to meet learning objectives. Recommend extra tutoring and closer monitoring of progress.';
        }
    }
}
