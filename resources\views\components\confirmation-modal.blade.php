<!-- Confirmation Modal Component -->
<div id="confirmation-modal" class="fixed inset-0 bg-gray-600/50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-6 w-80 max-w-sm shadow-lg rounded-lg bg-white border-0">
        <div class="text-center">
            <div id="modal-icon" class="mx-auto flex items-center justify-center h-12 w-12 rounded-full mb-4">
                <!-- Icon will be set dynamically -->
            </div>
            <h3 id="modal-title" class="text-lg leading-6 font-medium text-gray-900 mb-2">
                <!-- Title will be set dynamically -->
            </h3>
            <div id="modal-message" class="text-sm text-gray-500 mb-6">
                <!-- Message will be set dynamically -->
            </div>
            <div class="flex space-x-3 justify-center">
                <button id="modal-cancel" class="btn-cancel text-sm">
                    Cancel
                </button>
                <button id="modal-confirm" class="px-4 py-2 text-white text-sm font-medium rounded-md focus:outline-none focus:ring-2 transition-colors">
                    <!-- Button text and color will be set dynamically -->
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Custom confirmation modal system
window.confirmModal = function(options = {}) {
    return new Promise((resolve) => {
        const modal = document.getElementById('confirmation-modal');
        const modalIcon = document.getElementById('modal-icon');
        const modalTitle = document.getElementById('modal-title');
        const modalMessage = document.getElementById('modal-message');
        const modalCancel = document.getElementById('modal-cancel');
        const modalConfirm = document.getElementById('modal-confirm');

        // Set default options
        const config = {
            title: 'Confirm Action',
            message: 'Are you sure you want to proceed?',
            confirmText: 'Confirm',
            cancelText: 'Cancel',
            type: 'warning', // warning, danger, info, success
            showCancel: true,
            ...options
        };

        // Set modal content
        modalTitle.textContent = config.title;
        modalMessage.textContent = config.message;
        modalCancel.textContent = config.cancelText;
        modalConfirm.textContent = config.confirmText;

        // Set icon and colors based on type
        let iconHtml = '';
        let iconBgClass = '';
        let confirmBtnClass = '';

        switch(config.type) {
            case 'danger':
                iconHtml = `<svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>`;
                iconBgClass = 'bg-red-100';
                confirmBtnClass = 'bg-red-600 hover:bg-red-700 focus:ring-red-300';
                break;
            case 'success':
                iconHtml = `<svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>`;
                iconBgClass = 'bg-green-100';
                confirmBtnClass = 'bg-green-600 hover:bg-green-700 focus:ring-green-300';
                break;
            case 'info':
                iconHtml = `<svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>`;
                iconBgClass = 'bg-blue-100';
                confirmBtnClass = 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-300';
                break;
            default: // warning
                iconHtml = `<svg class="h-6 w-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>`;
                iconBgClass = 'bg-yellow-100';
                confirmBtnClass = 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-300';
        }

        modalIcon.className = `mx-auto flex items-center justify-center h-12 w-12 rounded-full mb-4 ${iconBgClass}`;
        modalIcon.innerHTML = iconHtml;
        modalConfirm.className = `px-4 py-2 text-white text-sm font-medium rounded-md focus:outline-none focus:ring-2 transition-colors ${confirmBtnClass}`;

        // Show/hide cancel button based on config
        if (config.showCancel) {
            modalCancel.style.display = 'inline-flex';
        } else {
            modalCancel.style.display = 'none';
        }

        // Show modal
        modal.classList.remove('hidden');

        // Handle events
        const handleConfirm = () => {
            modal.classList.add('hidden');
            resolve(true);
            cleanup();
        };

        const handleCancel = () => {
            modal.classList.add('hidden');
            resolve(false);
            cleanup();
        };

        const handleClickOutside = (e) => {
            if (e.target === modal) {
                handleCancel();
            }
        };

        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                handleCancel();
            }
        };

        const cleanup = () => {
            modalConfirm.removeEventListener('click', handleConfirm);
            modalCancel.removeEventListener('click', handleCancel);
            modal.removeEventListener('click', handleClickOutside);
            document.removeEventListener('keydown', handleEscape);
        };

        // Add event listeners
        modalConfirm.addEventListener('click', handleConfirm);
        modalCancel.addEventListener('click', handleCancel);
        modal.addEventListener('click', handleClickOutside);
        document.addEventListener('keydown', handleEscape);
    });
};

// Helper functions for common confirmation types
window.confirmDelete = function(itemName = 'this item') {
    return confirmModal({
        title: 'Delete Confirmation',
        message: `Are you sure you want to delete ${itemName}? This action cannot be undone.`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
        type: 'danger'
    });
};

window.confirmPayment = function(amount) {
    return confirmModal({
        title: 'Payment Confirmation',
        message: `You are about to make a payment of ${amount}. You will be redirected to the payment gateway to complete this transaction.`,
        confirmText: 'Proceed to Payment',
        cancelText: 'Cancel',
        type: 'info'
    });
};

window.confirmAction = function(action, message = null) {
    return confirmModal({
        title: `Confirm ${action}`,
        message: message || `Are you sure you want to ${action.toLowerCase()}?`,
        confirmText: action,
        cancelText: 'Cancel',
        type: 'warning'
    });
};
</script>
