@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="Add Announcement"
        description="Create a new announcement"
        :back-route="route('admin.announcements.index')"
        back-label="Back to Announcements">
    </x-page-header>

    <!-- Form -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Announcement Information</h3>
        </div>
        <form method="POST" action="{{ route('admin.announcements.store') }}" class="p-6">
            @csrf
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Title -->
                <div class="md:col-span-2">
                    <label for="title" class="block text-sm font-medium text-gray-700">Announcement Title *</label>
                    <input type="text" id="title" name="title" value="{{ old('title') }}" class="form-input mt-1" required placeholder="e.g., Important Notice: School Closure">
                    @error('title')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Type -->
                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700">Type *</label>
                    <select id="type" name="type" class="form-select mt-1" required>
                        <option value="">Select Type</option>
                        <option value="general" {{ old('type') == 'general' ? 'selected' : '' }}>General</option>
                        <option value="urgent" {{ old('type') == 'urgent' ? 'selected' : '' }}>Urgent</option>
                        <option value="academic" {{ old('type') == 'academic' ? 'selected' : '' }}>Academic</option>
                        <option value="event" {{ old('type') == 'event' ? 'selected' : '' }}>Event</option>
                        <option value="reminder" {{ old('type') == 'reminder' ? 'selected' : '' }}>Reminder</option>
                    </select>
                    @error('type')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Target Audience -->
                <div>
                    <label for="target_audience" class="block text-sm font-medium text-gray-700">Target Audience *</label>
                    <select id="target_audience" name="target_audience" class="form-select mt-1" required onchange="toggleTargetClasses()">
                        <option value="">Select Audience</option>
                        <option value="all" {{ old('target_audience') == 'all' ? 'selected' : '' }}>All</option>
                        <option value="students" {{ old('target_audience') == 'students' ? 'selected' : '' }}>Students</option>
                        <option value="teachers" {{ old('target_audience') == 'teachers' ? 'selected' : '' }}>Teachers</option>
                        <option value="parents" {{ old('target_audience') == 'parents' ? 'selected' : '' }}>Parents</option>
                        <option value="staff" {{ old('target_audience') == 'staff' ? 'selected' : '' }}>Staff</option>
                    </select>
                    @error('target_audience')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Publish Date -->
                <div>
                    <label for="publish_date" class="block text-sm font-medium text-gray-700">Publish Date *</label>
                    <input type="date" id="publish_date" name="publish_date" value="{{ old('publish_date', now()->format('Y-m-d')) }}" class="form-input mt-1" required>
                    @error('publish_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Expire Date -->
                <div>
                    <label for="expire_date" class="block text-sm font-medium text-gray-700">Expire Date</label>
                    <input type="date" id="expire_date" name="expire_date" value="{{ old('expire_date') }}" class="form-input mt-1">
                    <p class="mt-1 text-sm text-gray-500">Leave empty for no expiration</p>
                    @error('expire_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Target Classes (conditional) -->
            <div id="target-classes-section" class="mt-6" style="display: none;">
                <label class="block text-sm font-medium text-gray-700">Target Classes</label>
                <div class="mt-2 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                    @foreach($classes as $class)
                        <div class="flex items-center">
                            <input type="checkbox" id="class_{{ $class->id }}" name="target_classes[]" value="{{ $class->id }}" 
                                   {{ in_array($class->id, old('target_classes', [])) ? 'checked' : '' }} class="form-checkbox">
                            <label for="class_{{ $class->id }}" class="ml-2 text-sm text-gray-700">{{ $class->name }}</label>
                        </div>
                    @endforeach
                </div>
                @error('target_classes')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Content -->
            <div class="mt-6">
                <label for="content" class="block text-sm font-medium text-gray-700">Content *</label>
                <textarea id="content" name="content" rows="6" class="form-textarea mt-1" required placeholder="Write your announcement content here...">{{ old('content') }}</textarea>
                @error('content')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Status Options -->
            <div class="mt-6">
                <label class="block text-sm font-medium text-gray-700">Status Options</label>
                <div class="mt-2 space-y-2">
                    <div class="flex items-center">
                        <input type="checkbox" id="is_published" name="is_published" value="1" {{ old('is_published', true) ? 'checked' : '' }} class="form-checkbox">
                        <label for="is_published" class="ml-2 text-sm text-gray-700">Published</label>
                        <p class="ml-2 text-xs text-gray-500">(Uncheck to save as draft)</p>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="is_pinned" name="is_pinned" value="1" {{ old('is_pinned') ? 'checked' : '' }} class="form-checkbox">
                        <label for="is_pinned" class="ml-2 text-sm text-gray-700">Pin Announcement</label>
                        <p class="ml-2 text-xs text-gray-500">(Pinned announcements appear at the top)</p>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="mt-8 flex items-center justify-end space-x-3">
                <a href="{{ route('admin.announcements.index') }}" class="btn-cancel">
                    Cancel
                </a>
                <button type="submit" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Create Announcement
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Toggle target classes section based on target audience
function toggleTargetClasses() {
    const targetAudience = document.getElementById('target_audience').value;
    const targetClassesSection = document.getElementById('target-classes-section');
    
    if (targetAudience === 'students' || targetAudience === 'parents') {
        targetClassesSection.style.display = 'block';
    } else {
        targetClassesSection.style.display = 'none';
        // Uncheck all checkboxes when hiding
        const checkboxes = targetClassesSection.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => checkbox.checked = false);
    }
}

// Validate expire date is after publish date
document.getElementById('publish_date').addEventListener('change', function() {
    const publishDate = this.value;
    const expireDateInput = document.getElementById('expire_date');
    
    if (publishDate) {
        expireDateInput.min = publishDate;
        
        // If expire date is before publish date, clear it
        if (expireDateInput.value && expireDateInput.value <= publishDate) {
            expireDateInput.value = '';
        }
    }
});

document.getElementById('expire_date').addEventListener('change', function() {
    const expireDate = this.value;
    const publishDate = document.getElementById('publish_date').value;
    
    if (publishDate && expireDate && expireDate <= publishDate) {
        alert('Expire date must be after publish date');
        this.value = '';
    }
});

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleTargetClasses();
});
</script>
@endpush
