<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Guardian;
use App\Models\User;
use App\Models\Student;
use App\Models\Invoice;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class GuardianController extends Controller
{
    /**
     * Display a listing of guardians
     */
    public function index(Request $request)
    {
        $query = Guardian::with(['user', 'students.user']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->whereHas('user', function ($userQuery) use ($search) {
                    $userQuery->where('name', 'like', "%{$search}%")
                              ->orWhere('email', 'like', "%{$search}%")
                              ->orWhere('phone', 'like', "%{$search}%")
                              ->orWhere('address', 'like', "%{$search}%");
                })->orWhere('relationship', 'like', "%{$search}%")
                  ->orWhere('occupation', 'like', "%{$search}%")
                  ->orWhere('workplace', 'like', "%{$search}%")
                  ->orWhere('emergency_contact', 'like', "%{$search}%")
                  ->orWhere('monthly_income', 'like', "%{$search}%")
                  ->orWhereHas('students.user', function ($studentQuery) use ($search) {
                      $studentQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by relationship
        if ($request->filled('relationship')) {
            $query->where('relationship', $request->relationship);
        }

        // Filter by occupation
        if ($request->filled('occupation')) {
            $query->where('occupation', 'like', "%{$request->occupation}%");
        }

        // Filter by status
        if ($request->filled('status')) {
            $isActive = $request->status === 'active';
            $query->whereHas('user', function ($q) use ($isActive) {
                $q->where('is_active', $isActive);
            });
        }

        // Filter by income range
        if ($request->filled('income_min')) {
            $query->where('monthly_income', '>=', $request->income_min);
        }

        if ($request->filled('income_max')) {
            $query->where('monthly_income', '<=', $request->income_max);
        }

        $guardians = $query->latest()->paginate(20);

        // Get filter options
        $relationships = Guardian::distinct()->pluck('relationship')->filter()->sort();
        $occupations = Guardian::distinct()->pluck('occupation')->filter()->sort();

        $stats = [
            'total' => Guardian::count(),
            'active' => Guardian::whereHas('user', function ($q) {
                $q->where('is_active', true);
            })->count(),
            'inactive' => Guardian::whereHas('user', function ($q) {
                $q->where('is_active', false);
            })->count(),
            'with_children' => Guardian::whereHas('students')->count(),
            'total_children' => Guardian::withCount('students')->get()->sum('students_count'),
            'avg_income' => Guardian::whereNotNull('monthly_income')->avg('monthly_income'),
        ];

        return view('admin.guardians.index', compact('guardians', 'relationships', 'occupations', 'stats'));
    }

    /**
     * Show the form for creating a new guardian
     */
    public function create()
    {
        $relationships = Guardian::distinct()->pluck('relationship')->filter()->sort();
        $occupations = Guardian::distinct()->pluck('occupation')->filter()->sort();
        $students = Student::with('user')->get();
        
        return view('admin.guardians.create', compact('relationships', 'occupations', 'students'));
    }

    /**
     * Store a newly created guardian
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'relationship' => 'required|string|max:100',
            'occupation' => 'nullable|string|max:255',
            'workplace' => 'nullable|string|max:255',
            'monthly_income' => 'nullable|numeric|min:0',
            'emergency_contact' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'phone' => 'nullable|string|max:20',
            'student_ids' => 'nullable|array',
            'student_ids.*' => 'exists:students,id',
        ]);

        try {
            DB::beginTransaction();

            // Create user account
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'role' => 'guardian',
                'is_active' => true,
                'address' => $request->address,
                'phone' => $request->phone,
            ]);

            // Create guardian profile
            $guardian = Guardian::create([
                'user_id' => $user->id,
                'relationship' => $request->relationship,
                'occupation' => $request->occupation,
                'workplace' => $request->workplace,
                'monthly_income' => $request->monthly_income,
                'emergency_contact' => $request->emergency_contact,
            ]);

            // Attach students if provided
            if ($request->filled('student_ids')) {
                $guardian->students()->attach($request->student_ids);
            }

            DB::commit();

            return redirect()->route('admin.guardians.index')
                           ->with('success', 'Guardian created successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'Failed to create guardian. Please try again.');
        }
    }

    /**
     * Display the specified guardian
     */
    public function show(Guardian $guardian)
    {
        $guardian->load(['user', 'students.user']);

        // Get guardian's financial statistics
        $studentIds = $guardian->students->pluck('id');
        
        $financialStats = [
            'total_invoices' => Invoice::whereIn('student_id', $studentIds)->count(),
            'pending_invoices' => Invoice::whereIn('student_id', $studentIds)->where('status', 'sent')->count(),
            'paid_invoices' => Invoice::whereIn('student_id', $studentIds)->where('status', 'paid')->count(),
            'overdue_invoices' => Invoice::whereIn('student_id', $studentIds)->where('status', 'overdue')->count(),
            'total_amount_due' => Invoice::whereIn('student_id', $studentIds)
                                        ->whereIn('status', ['sent', 'overdue'])
                                        ->sum('total_amount'),
            'total_amount_paid' => Payment::where('paid_by', $guardian->user_id)
                                         ->where('status', 'paid')
                                         ->sum('amount'),
        ];

        // Get recent invoices for guardian's children
        $recentInvoices = Invoice::with(['student.user'])
                                ->whereIn('student_id', $studentIds)
                                ->latest()
                                ->take(10)
                                ->get();

        // Get recent payments made by guardian
        $recentPayments = Payment::with(['invoice.student.user'])
                                ->where('paid_by', $guardian->user_id)
                                ->where('status', 'paid')
                                ->latest()
                                ->take(10)
                                ->get();

        return view('admin.guardians.show', compact('guardian', 'financialStats', 'recentInvoices', 'recentPayments'));
    }

    /**
     * Show the form for editing the specified guardian
     */
    public function edit(Guardian $guardian)
    {
        $guardian->load(['user', 'students']);
        $relationships = Guardian::distinct()->pluck('relationship')->filter()->sort();
        $occupations = Guardian::distinct()->pluck('occupation')->filter()->sort();
        $students = Student::with('user')->get();
        
        return view('admin.guardians.edit', compact('guardian', 'relationships', 'occupations', 'students'));
    }

    /**
     * Update the specified guardian
     */
    public function update(Request $request, Guardian $guardian)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($guardian->user_id)],
            'password' => 'nullable|string|min:8|confirmed',
            'relationship' => 'required|string|max:100',
            'occupation' => 'nullable|string|max:255',
            'workplace' => 'nullable|string|max:255',
            'monthly_income' => 'nullable|numeric|min:0',
            'emergency_contact' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'phone' => 'nullable|string|max:20',
            'is_active' => 'boolean',
            'student_ids' => 'nullable|array',
            'student_ids.*' => 'exists:students,id',
        ]);

        try {
            DB::beginTransaction();

            // Update user account
            $userData = [
                'name' => $request->name,
                'email' => $request->email,
                'is_active' => $request->boolean('is_active', true),
                'address' => $request->address,
                'phone' => $request->phone,
            ];

            if ($request->filled('password')) {
                $userData['password'] = Hash::make($request->password);
            }

            $guardian->user()->update($userData);

            // Update guardian profile
            $guardian->update([
                'relationship' => $request->relationship,
                'occupation' => $request->occupation,
                'workplace' => $request->workplace,
                'monthly_income' => $request->monthly_income,
                'emergency_contact' => $request->emergency_contact,
            ]);

            // Update student relationships
            if ($request->has('student_ids')) {
                $guardian->students()->sync($request->student_ids ?? []);
            }

            DB::commit();

            return redirect()->route('admin.guardians.show', $guardian)
                           ->with('success', 'Guardian updated successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'Failed to update guardian. Please try again.');
        }
    }

    /**
     * Remove the specified guardian
     */
    public function destroy(Guardian $guardian)
    {
        try {
            DB::beginTransaction();

            // Check if guardian has any payments
            if (Payment::where('paid_by', $guardian->user_id)->exists()) {
                return back()->with('error', 'Cannot delete guardian with existing payments.');
            }

            // Detach students
            $guardian->students()->detach();

            // Delete guardian and user
            $user = $guardian->user;
            $guardian->delete();
            $user->delete();

            DB::commit();

            return redirect()->route('admin.guardians.index')
                           ->with('success', 'Guardian deleted successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Failed to delete guardian. Please try again.');
        }
    }

    /**
     * Toggle guardian status (active/inactive)
     */
    public function toggleStatus(Guardian $guardian)
    {
        try {
            $guardian->user()->update([
                'is_active' => !$guardian->user->is_active
            ]);

            $status = $guardian->user->is_active ? 'activated' : 'deactivated';
            
            return redirect()->route('admin.guardians.show', $guardian)
                           ->with('success', "Guardian {$status} successfully.");

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to update guardian status. Please try again.');
        }
    }
}
