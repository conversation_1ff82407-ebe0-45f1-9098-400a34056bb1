<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Grade;
use App\Models\Student;
use App\Models\Subject;
use App\Models\AcademicYear;
use App\Models\AcademicTerm;
use App\Models\GradeCategory;
use App\Models\ClassModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class GradeReportController extends Controller
{
    public function index(Request $request)
    {
        $academicYears = AcademicYear::orderBy('name')->get();
        $academicTerms = AcademicTerm::orderBy('name')->get();
        $gradeCategories = GradeCategory::where('is_active', true)->orderBy('name')->get();
        $subjects = Subject::where('is_active', true)->orderBy('name')->get();
        $classRooms = ClassModel::where('is_active', true)->orderBy('name')->get();

        // Statistics
        $stats = [
            'total_students' => Student::where('is_active', true)->count(),
            'total_grades' => Grade::count(),
            'average_performance' => Grade::avg('percentage'),
            'pass_rate' => Grade::where('is_passed', true)->count() / max(Grade::count(), 1) * 100,
        ];

        return view('admin.grade-reports.index', compact(
            'academicYears',
            'academicTerms',
            'gradeCategories',
            'subjects',
            'classRooms',
            'stats'
        ));
    }

    public function studentReport(Request $request)
    {
        $validated = $request->validate([
            'student_id' => 'required|exists:students,id',
            'academic_year_id' => 'required|exists:academic_years,id',
            'academic_term_id' => 'nullable|exists:academic_terms,id',
        ]);

        $student = Student::with(['currentClass', 'currentEnrollment'])->findOrFail($validated['student_id']);
        $academicYear = AcademicYear::findOrFail($validated['academic_year_id']);
        $academicTerm = $validated['academic_term_id'] ? AcademicTerm::findOrFail($validated['academic_term_id']) : null;

        $query = Grade::where('student_id', $student->id)
                     ->where('academic_year_id', $academicYear->id)
                     ->with(['subject', 'gradeCategory', 'academicTerm']);

        if ($academicTerm) {
            $query->where('academic_term_id', $academicTerm->id);
        }

        $grades = $query->get();

        // Group grades by subject and category
        $gradesBySubject = $grades->groupBy('subject.name');
        
        // Calculate overall statistics
        $overallStats = [
            'total_subjects' => $gradesBySubject->count(),
            'total_grades' => $grades->count(),
            'average_percentage' => $grades->avg('percentage'),
            'total_grade_points' => $grades->sum('grade_point'),
            'gpa' => $grades->count() > 0 ? $grades->avg('grade_point') : 0,
            'passed_subjects' => $gradesBySubject->filter(function ($subjectGrades) {
                return $subjectGrades->avg('percentage') >= 50;
            })->count(),
        ];

        return view('admin.grade-reports.student-report', compact(
            'student',
            'academicYear',
            'academicTerm',
            'grades',
            'gradesBySubject',
            'overallStats'
        ));
    }

    public function classReport(Request $request)
    {
        $validated = $request->validate([
            'class_room_id' => 'required|exists:classes,id',
            'academic_year_id' => 'required|exists:academic_years,id',
            'academic_term_id' => 'nullable|exists:academic_terms,id',
            'subject_id' => 'nullable|exists:subjects,id',
        ]);

        $classRoom = ClassModel::with(['activeStudents'])->findOrFail($validated['class_room_id']);
        $academicYear = AcademicYear::findOrFail($validated['academic_year_id']);
        $academicTerm = $validated['academic_term_id'] ? AcademicTerm::findOrFail($validated['academic_term_id']) : null;
        $subject = $validated['subject_id'] ? Subject::findOrFail($validated['subject_id']) : null;

        $query = Grade::whereIn('student_id', $classRoom->activeStudents->pluck('id'))
                     ->where('academic_year_id', $academicYear->id)
                     ->with(['student', 'subject', 'gradeCategory']);

        if ($academicTerm) {
            $query->where('academic_term_id', $academicTerm->id);
        }

        if ($subject) {
            $query->where('subject_id', $subject->id);
        }

        $grades = $query->get();

        // Group grades by student
        $gradesByStudent = $grades->groupBy('student_id');
        
        // Calculate class statistics
        $classStats = [
            'total_students' => $classRoom->activeStudents->count(),
            'students_with_grades' => $gradesByStudent->count(),
            'total_grades' => $grades->count(),
            'class_average' => $grades->avg('percentage'),
            'highest_score' => $grades->max('percentage'),
            'lowest_score' => $grades->min('percentage'),
            'pass_rate' => $gradesByStudent->filter(function ($studentGrades) {
                return $studentGrades->avg('percentage') >= 50;
            })->count() / max($gradesByStudent->count(), 1) * 100,
        ];

        // Subject performance if specific subject selected
        $subjectPerformance = null;
        if ($subject) {
            $subjectGrades = $grades->where('subject_id', $subject->id);
            $subjectPerformance = [
                'subject_name' => $subject->name,
                'total_students' => $subjectGrades->groupBy('student_id')->count(),
                'average_score' => $subjectGrades->avg('percentage'),
                'highest_score' => $subjectGrades->max('percentage'),
                'lowest_score' => $subjectGrades->min('percentage'),
                'pass_rate' => $subjectGrades->where('is_passed', true)->count() / max($subjectGrades->count(), 1) * 100,
            ];
        }

        return view('admin.grade-reports.class-report', compact(
            'classRoom',
            'academicYear',
            'academicTerm',
            'subject',
            'grades',
            'gradesByStudent',
            'classStats',
            'subjectPerformance'
        ));
    }

    public function subjectReport(Request $request)
    {
        $validated = $request->validate([
            'subject_id' => 'required|exists:subjects,id',
            'academic_year_id' => 'required|exists:academic_years,id',
            'academic_term_id' => 'nullable|exists:academic_terms,id',
            'class_room_id' => 'nullable|exists:classes,id',
        ]);

        $subject = Subject::findOrFail($validated['subject_id']);
        $academicYear = AcademicYear::findOrFail($validated['academic_year_id']);
        $academicTerm = $validated['academic_term_id'] ? AcademicTerm::findOrFail($validated['academic_term_id']) : null;
        $classRoom = $validated['class_room_id'] ? ClassModel::findOrFail($validated['class_room_id']) : null;

        $query = Grade::where('subject_id', $subject->id)
                     ->where('academic_year_id', $academicYear->id)
                     ->with(['student', 'gradeCategory']);

        if ($academicTerm) {
            $query->where('academic_term_id', $academicTerm->id);
        }

        if ($classRoom) {
            $query->whereHas('student.currentEnrollment', function ($q) use ($classRoom) {
                $q->where('class_id', $classRoom->id);
            });
        }

        $grades = $query->get();

        // Group grades by category
        $gradesByCategory = $grades->groupBy('gradeCategory.name');
        
        // Calculate subject statistics
        $subjectStats = [
            'total_students' => $grades->groupBy('student_id')->count(),
            'total_assessments' => $grades->count(),
            'average_score' => $grades->avg('percentage'),
            'highest_score' => $grades->max('percentage'),
            'lowest_score' => $grades->min('percentage'),
            'pass_rate' => $grades->where('is_passed', true)->count() / max($grades->count(), 1) * 100,
            'grade_distribution' => $grades->groupBy('grade_letter')->map->count(),
        ];

        return view('admin.grade-reports.subject-report', compact(
            'subject',
            'academicYear',
            'academicTerm',
            'classRoom',
            'grades',
            'gradesByCategory',
            'subjectStats'
        ));
    }

    public function termReport(Request $request)
    {
        $validated = $request->validate([
            'academic_year_id' => 'required|exists:academic_years,id',
            'academic_term_id' => 'required|exists:academic_terms,id',
            'class_room_id' => 'nullable|exists:classes,id',
        ]);

        $academicYear = AcademicYear::findOrFail($validated['academic_year_id']);
        $academicTerm = AcademicTerm::findOrFail($validated['academic_term_id']);
        $classRoom = $validated['class_room_id'] ? ClassModel::findOrFail($validated['class_room_id']) : null;

        $query = Grade::where('academic_year_id', $academicYear->id)
                     ->where('academic_term_id', $academicTerm->id)
                     ->with(['student', 'subject', 'gradeCategory']);

        if ($classRoom) {
            $query->whereHas('student.currentEnrollment', function ($q) use ($classRoom) {
                $q->where('class_id', $classRoom->id);
            });
        }

        $grades = $query->get();

        // Group grades by student and subject
        $gradesByStudent = $grades->groupBy('student_id');
        $gradesBySubject = $grades->groupBy('subject_id');
        
        // Calculate term statistics
        $termStats = [
            'total_students' => $gradesByStudent->count(),
            'total_subjects' => $gradesBySubject->count(),
            'total_assessments' => $grades->count(),
            'term_average' => $grades->avg('percentage'),
            'highest_gpa' => $gradesByStudent->map(function ($studentGrades) {
                return $studentGrades->avg('grade_point');
            })->max(),
            'lowest_gpa' => $gradesByStudent->map(function ($studentGrades) {
                return $studentGrades->avg('grade_point');
            })->min(),
            'overall_pass_rate' => $gradesByStudent->filter(function ($studentGrades) {
                return $studentGrades->avg('percentage') >= 50;
            })->count() / max($gradesByStudent->count(), 1) * 100,
        ];

        return view('admin.grade-reports.term-report', compact(
            'academicYear',
            'academicTerm',
            'classRoom',
            'grades',
            'gradesByStudent',
            'gradesBySubject',
            'termStats'
        ));
    }

    public function exportReport(Request $request)
    {
        $reportType = $request->input('report_type');
        
        switch ($reportType) {
            case 'student':
                return $this->exportStudentReport($request);
            case 'class':
                return $this->exportClassReport($request);
            case 'subject':
                return $this->exportSubjectReport($request);
            case 'term':
                return $this->exportTermReport($request);
            default:
                return back()->with('error', 'Invalid report type.');
        }
    }

    private function exportStudentReport(Request $request)
    {
        // Implementation for student report export
        return back()->with('success', 'Student report exported successfully.');
    }

    private function exportClassReport(Request $request)
    {
        // Implementation for class report export
        return back()->with('success', 'Class report exported successfully.');
    }

    private function exportSubjectReport(Request $request)
    {
        // Implementation for subject report export
        return back()->with('success', 'Subject report exported successfully.');
    }

    private function exportTermReport(Request $request)
    {
        // Implementation for term report export
        return back()->with('success', 'Term report exported successfully.');
    }
}
