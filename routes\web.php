<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Admin\DashboardController as AdminDashboardController;
use App\Http\Controllers\Teacher\DashboardController as TeacherDashboardController;
use App\Http\Controllers\Guardian\DashboardController as GuardianDashboardController;
use App\Http\Controllers\Guardian\ChildrenController as GuardianChildrenController;
use App\Http\Controllers\Student\DashboardController as StudentDashboardController;
use App\Http\Controllers\Admin\InvoiceController as AdminInvoiceController;
use App\Http\Controllers\Admin\PaymentController as AdminPaymentController;
use App\Http\Controllers\Admin\StudentController as AdminStudentController;
use App\Http\Controllers\Admin\TeacherController as AdminTeacherController;
use App\Http\Controllers\Admin\GuardianController as AdminGuardianController;
use App\Http\Controllers\Guardian\InvoiceController as GuardianInvoiceController;
use App\Http\Controllers\Guardian\PaymentController as GuardianPaymentController;
use App\Http\Controllers\Teacher\InvoiceController as TeacherInvoiceController;
use App\Http\Controllers\BillplzWebhookController;

// Redirect root to login
Route::get('/', function () {
    return redirect()->route('login');
});

// Authentication Routes
Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('/login', [LoginController::class, 'login']);
Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

// Admin Routes
Route::middleware(['auth', 'role:admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');

    // Invoice Management
    Route::resource('invoices', AdminInvoiceController::class);
    Route::post('/invoices/{invoice}/send', [AdminInvoiceController::class, 'send'])->name('invoices.send');
    Route::post('/invoices/{invoice}/cancel', [AdminInvoiceController::class, 'cancel'])->name('invoices.cancel');

    // Payment Management
    Route::resource('payments', AdminPaymentController::class)->only(['index', 'show']);
    Route::post('/payments/{payment}/refund', [AdminPaymentController::class, 'refund'])->name('payments.refund');

    // Student Management
    Route::resource('students', AdminStudentController::class);
    Route::post('/students/{student}/toggle-status', [AdminStudentController::class, 'toggleStatus'])->name('students.toggle-status');

    // Teacher Management
    Route::resource('teachers', AdminTeacherController::class);
    Route::post('/teachers/{teacher}/toggle-status', [AdminTeacherController::class, 'toggleStatus'])->name('teachers.toggle-status');

    // Guardian Management
    Route::resource('guardians', AdminGuardianController::class);
    Route::post('/guardians/{guardian}/toggle-status', [AdminGuardianController::class, 'toggleStatus'])->name('guardians.toggle-status');

    // Relationships Overview
    Route::get('/relationships', [App\Http\Controllers\Admin\RelationshipController::class, 'index'])->name('relationships.index');
    Route::get('/relationships/family/{familyName}', [App\Http\Controllers\Admin\RelationshipController::class, 'showFamily'])->name('relationships.family');

    // Attendance Management
    Route::prefix('attendance')->name('attendance.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\AttendanceController::class, 'index'])->name('index');
        Route::get('/students', [App\Http\Controllers\Admin\AttendanceController::class, 'students'])->name('students');
        Route::get('/teachers', [App\Http\Controllers\Admin\AttendanceController::class, 'teachers'])->name('teachers');
        Route::post('/mark-student', [App\Http\Controllers\Admin\AttendanceController::class, 'markStudent'])->name('mark-student');
        Route::post('/mark-teacher', [App\Http\Controllers\Admin\AttendanceController::class, 'markTeacher'])->name('mark-teacher');
        Route::post('/bulk-mark-students', [App\Http\Controllers\Admin\AttendanceController::class, 'bulkMarkStudents'])->name('bulk-mark-students');
        Route::post('/bulk-mark-teachers', [App\Http\Controllers\Admin\AttendanceController::class, 'bulkMarkTeachers'])->name('bulk-mark-teachers');
        Route::post('/mark-my-attendance', [App\Http\Controllers\Admin\AttendanceController::class, 'markMyAttendance'])->name('mark-my-attendance');
        Route::get('/reports', [App\Http\Controllers\Admin\AttendanceController::class, 'reports'])->name('reports');
    });

    // Activity Logs Routes
    Route::prefix('activity-logs')->name('activity-logs.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\ActivityLogController::class, 'index'])->name('index');
        Route::get('/{activityLog}', [App\Http\Controllers\Admin\ActivityLogController::class, 'show'])->name('show');
        Route::get('/export/csv', [App\Http\Controllers\Admin\ActivityLogController::class, 'export'])->name('export');
        Route::post('/clear', [App\Http\Controllers\Admin\ActivityLogController::class, 'clear'])->name('clear');
    });

    // Academic Management Routes
    Route::prefix('academic')->name('academic.')->group(function () {
        // Subject Management - Define specific routes before resource
        Route::post('/subjects/undo', [App\Http\Controllers\Admin\SubjectController::class, 'undo'])->name('subjects.undo');
        Route::post('/subjects/{subject}/toggle-status', [App\Http\Controllers\Admin\SubjectController::class, 'toggleStatus'])->name('subjects.toggle-status');
        Route::get('/subjects/{subject}/classes', [App\Http\Controllers\Admin\SubjectController::class, 'getClasses'])->name('subjects.classes');
        Route::get('/subjects/{subject}/teachers', [App\Http\Controllers\Admin\SubjectController::class, 'getTeachers'])->name('subjects.teachers');
        Route::get('/subjects/{subject}/check-dependencies', [App\Http\Controllers\Admin\SubjectController::class, 'checkDependencies'])->name('subjects.check-dependencies');
        Route::delete('/subjects/{subject}/force-destroy', [App\Http\Controllers\Admin\SubjectController::class, 'forceDestroy'])->name('subjects.force-destroy');

        // Resource route for subjects
        Route::resource('subjects', App\Http\Controllers\Admin\SubjectController::class);

        // Class Management
        Route::resource('classes', App\Http\Controllers\Admin\ClassController::class);
        Route::post('/classes/{class}/toggle-status', [App\Http\Controllers\Admin\ClassController::class, 'toggleStatus'])->name('classes.toggle-status');
        Route::get('/classes/{classId}/sections', [App\Http\Controllers\Admin\ClassController::class, 'getSections'])->name('classes.sections');
        Route::get('/classes/{classId}/students', [App\Http\Controllers\Admin\ClassController::class, 'getStudents'])->name('classes.students');
        Route::get('/classes/{classId}/subjects', [App\Http\Controllers\Admin\ClassController::class, 'getSubjects'])->name('classes.subjects');

        // Section Management
        Route::resource('sections', App\Http\Controllers\Admin\SectionController::class);
        Route::post('/sections/{section}/toggle-status', [App\Http\Controllers\Admin\SectionController::class, 'toggleStatus'])->name('sections.toggle-status');
    });

    // Academic Calendar & Schedule Management
    Route::resource('academic-years', App\Http\Controllers\Admin\AcademicYearController::class);
    Route::post('/academic-years/{academicYear}/set-current', [App\Http\Controllers\Admin\AcademicYearController::class, 'setCurrent'])->name('academic-years.set-current');

    Route::resource('academic-terms', App\Http\Controllers\Admin\AcademicTermController::class);
    Route::post('/academic-terms/{academicTerm}/set-current', [App\Http\Controllers\Admin\AcademicTermController::class, 'setCurrent'])->name('academic-terms.set-current');

    Route::resource('time-slots', App\Http\Controllers\Admin\TimeSlotController::class);
    Route::resource('schedules', App\Http\Controllers\Admin\ScheduleController::class);
    Route::get('/schedules/data', [App\Http\Controllers\Admin\ScheduleController::class, 'getScheduleData'])->name('schedules.data');

    Route::resource('school-events', App\Http\Controllers\Admin\SchoolEventController::class);
    Route::resource('announcements', App\Http\Controllers\Admin\AnnouncementController::class);
    Route::post('/announcements/{announcement}/toggle-pin', [App\Http\Controllers\Admin\AnnouncementController::class, 'togglePin'])->name('announcements.toggle-pin');

    // Academic Calendar
    Route::get('/academic-calendar', [App\Http\Controllers\Admin\AcademicCalendarController::class, 'index'])->name('academic-calendar.index');
    Route::get('/academic-calendar/teacher-schedule', [App\Http\Controllers\Admin\AcademicCalendarController::class, 'teacherSchedule'])->name('academic-calendar.teacher-schedule');
    Route::get('/academic-calendar/schedule-data', [App\Http\Controllers\Admin\AcademicCalendarController::class, 'getScheduleData'])->name('academic-calendar.schedule-data');

    // Grading System Management
    Route::prefix('grading')->name('grading.')->group(function () {
        // Grade Scales
        Route::resource('grade-scales', App\Http\Controllers\Admin\GradeScaleController::class);
        Route::post('/grade-scales/{gradeScale}/toggle-status', [App\Http\Controllers\Admin\GradeScaleController::class, 'toggleStatus'])->name('grade-scales.toggle-status');
        Route::post('/grade-scales/{gradeScale}/set-default', [App\Http\Controllers\Admin\GradeScaleController::class, 'setDefault'])->name('grade-scales.set-default');

        // Grade Categories
        Route::resource('grade-categories', App\Http\Controllers\Admin\GradeCategoryController::class);
        Route::post('/grade-categories/{gradeCategory}/toggle-status', [App\Http\Controllers\Admin\GradeCategoryController::class, 'toggleStatus'])->name('grade-categories.toggle-status');

        // Exams
        Route::resource('exams', App\Http\Controllers\Admin\ExamController::class);
        Route::post('/exams/{exam}/publish', [App\Http\Controllers\Admin\ExamController::class, 'publish'])->name('exams.publish');
        Route::post('/exams/{exam}/schedule', [App\Http\Controllers\Admin\ExamController::class, 'schedule'])->name('exams.schedule');

        // Assignments
        Route::resource('assignments', App\Http\Controllers\Admin\AssignmentController::class);
        Route::get('/assignments/{assignment}/submissions', [App\Http\Controllers\Admin\AssignmentController::class, 'submissions'])->name('assignments.submissions');
        Route::post('/assignments/{assignment}/toggle-status', [App\Http\Controllers\Admin\AssignmentController::class, 'toggleStatus'])->name('assignments.toggle-status');
        Route::post('/assignments/{assignment}/toggle-published', [App\Http\Controllers\Admin\AssignmentController::class, 'togglePublished'])->name('assignments.toggle-published');
        Route::post('/assignments/{assignment}/duplicate', [App\Http\Controllers\Admin\AssignmentController::class, 'duplicate'])->name('assignments.duplicate');
        Route::post('/assignments/undo', [App\Http\Controllers\Admin\AssignmentController::class, 'undo'])->name('assignments.undo');

        // Grades
        Route::resource('grades', App\Http\Controllers\Admin\GradeController::class);
        Route::get('/grades/bulk-entry', [App\Http\Controllers\Admin\GradeController::class, 'bulkEntry'])->name('grades.bulk-entry');
        Route::post('/grades/bulk-store', [App\Http\Controllers\Admin\GradeController::class, 'storeBulk'])->name('grades.bulk-store');
        Route::post('/grades/{grade}/toggle-published', [App\Http\Controllers\Admin\GradeController::class, 'togglePublished'])->name('grades.toggle-published');

        // Grade Reports
        Route::get('/grade-reports', [App\Http\Controllers\Admin\GradeReportController::class, 'index'])->name('grade-reports.index');
        Route::get('/grade-reports/student-report', [App\Http\Controllers\Admin\GradeReportController::class, 'studentReport'])->name('grade-reports.student-report');
        Route::get('/grade-reports/class-report', [App\Http\Controllers\Admin\GradeReportController::class, 'classReport'])->name('grade-reports.class-report');
        Route::get('/grade-reports/subject-report', [App\Http\Controllers\Admin\GradeReportController::class, 'subjectReport'])->name('grade-reports.subject-report');
        Route::get('/grade-reports/term-report', [App\Http\Controllers\Admin\GradeReportController::class, 'termReport'])->name('grade-reports.term-report');
        Route::post('/grade-reports/export', [App\Http\Controllers\Admin\GradeReportController::class, 'exportReport'])->name('grade-reports.export');
    });
});

// Teacher Routes
Route::middleware(['auth', 'role:teacher'])->prefix('teacher')->name('teacher.')->group(function () {
    Route::get('/dashboard', [TeacherDashboardController::class, 'index'])->name('dashboard');

    // Invoice Management (Teachers can create invoices for their students)
    Route::resource('invoices', TeacherInvoiceController::class);
    Route::post('/invoices/{invoice}/send', [TeacherInvoiceController::class, 'send'])->name('invoices.send');

    // Grades
    Route::get('/grades', [App\Http\Controllers\Teacher\GradeController::class, 'index'])->name('grades.index');
    Route::get('/grades/subject/{subject}', [App\Http\Controllers\Teacher\GradeController::class, 'subject'])->name('grades.subject');
    Route::get('/grades/class/{class}', [App\Http\Controllers\Teacher\GradeController::class, 'class'])->name('grades.class');

    // Placeholder routes for sidebar links
    Route::get('/students', function () { return view('teacher.students.index'); })->name('students.index');
});

// Guardian Routes
Route::middleware(['auth', 'role:guardian'])->prefix('guardian')->name('guardian.')->group(function () {
    Route::get('/dashboard', [GuardianDashboardController::class, 'index'])->name('dashboard');

    // Invoice Management (View and pay invoices)
    Route::get('/invoices', [GuardianInvoiceController::class, 'index'])->name('invoices.index');
    Route::get('/invoices/{invoice}', [GuardianInvoiceController::class, 'show'])->name('invoices.show');
    Route::post('/invoices/{invoice}/pay', [GuardianInvoiceController::class, 'pay'])->name('invoices.pay');

    // Payment Management (View payment history)
    Route::get('/payments', [GuardianPaymentController::class, 'index'])->name('payments.index');
    Route::get('/payments/{payment}', [GuardianPaymentController::class, 'show'])->name('payments.show');
    Route::get('/payments/{payment}/receipt', [GuardianPaymentController::class, 'receipt'])->name('payments.receipt');

    // Children Management
    Route::get('/children', [GuardianChildrenController::class, 'index'])->name('children.index');

    // Grades
    Route::get('/grades', [App\Http\Controllers\Guardian\GradeController::class, 'index'])->name('grades.index');
    Route::get('/grades/{student}/subject/{subject}', [App\Http\Controllers\Guardian\GradeController::class, 'subject'])->name('grades.subject');
});

// Student Routes
Route::middleware(['auth', 'role:student'])->prefix('student')->name('student.')->group(function () {
    Route::get('/dashboard', [StudentDashboardController::class, 'index'])->name('dashboard');

    // Grades
    Route::get('/grades', [App\Http\Controllers\Student\GradeController::class, 'index'])->name('grades.index');
    Route::get('/grades/subject/{subject}', [App\Http\Controllers\Student\GradeController::class, 'subject'])->name('grades.subject');
});

// Billplz Webhook Routes (No authentication required)
Route::post('/billplz/webhook', [BillplzWebhookController::class, 'handle'])->name('billplz.webhook');
Route::get('/payment/return/{payment}', [BillplzWebhookController::class, 'return'])->name('payment.return');
Route::get('/payment/callback/{payment}', [BillplzWebhookController::class, 'callback'])->name('payment.callback');
