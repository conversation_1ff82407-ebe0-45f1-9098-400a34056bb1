@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="Edit Guardian"
        description="Update guardian profile and account information"
        :back-route="route('admin.guardians.show', $guardian)"
        back-label="Back to Profile">
    </x-page-header>

    <form method="POST" action="{{ route('admin.guardians.update', $guardian) }}" class="space-y-6">
        @csrf
        @method('PUT')
        
        <!-- Personal Information -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Personal Information</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Full Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                    <input type="text" name="name" id="name" value="{{ old('name', $guardian->user->name) }}" 
                           class="form-input @error('name') border-red-500 @enderror" 
                           placeholder="Enter guardian's full name" required>
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Relationship -->
                <div>
                    <label for="relationship" class="block text-sm font-medium text-gray-700 mb-2">Relationship</label>
                    <select name="relationship" id="relationship" class="form-select @error('relationship') border-red-500 @enderror" required>
                        <option value="">Select relationship</option>
                        <option value="Father" {{ old('relationship', $guardian->relationship) == 'Father' ? 'selected' : '' }}>Father</option>
                        <option value="Mother" {{ old('relationship', $guardian->relationship) == 'Mother' ? 'selected' : '' }}>Mother</option>
                        <option value="Guardian" {{ old('relationship', $guardian->relationship) == 'Guardian' ? 'selected' : '' }}>Guardian</option>
                        <option value="Grandfather" {{ old('relationship', $guardian->relationship) == 'Grandfather' ? 'selected' : '' }}>Grandfather</option>
                        <option value="Grandmother" {{ old('relationship', $guardian->relationship) == 'Grandmother' ? 'selected' : '' }}>Grandmother</option>
                        <option value="Uncle" {{ old('relationship', $guardian->relationship) == 'Uncle' ? 'selected' : '' }}>Uncle</option>
                        <option value="Aunt" {{ old('relationship', $guardian->relationship) == 'Aunt' ? 'selected' : '' }}>Aunt</option>
                        <option value="Other" {{ old('relationship', $guardian->relationship) == 'Other' ? 'selected' : '' }}>Other</option>
                    </select>
                    @error('relationship')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Email -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                    <input type="email" name="email" id="email" value="{{ old('email', $guardian->user->email) }}" 
                           class="form-input @error('email') border-red-500 @enderror" 
                           placeholder="<EMAIL>" required>
                    @error('email')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Phone -->
                <div>
                    <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                    <input type="text" name="phone" id="phone" value="{{ old('phone', $guardian->user->phone) }}" 
                           class="form-input @error('phone') border-red-500 @enderror" 
                           placeholder="+60123456789">
                    @error('phone')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Emergency Contact -->
                <div>
                    <label for="emergency_contact" class="block text-sm font-medium text-gray-700 mb-2">Emergency Contact</label>
                    <input type="text" name="emergency_contact" id="emergency_contact" value="{{ old('emergency_contact', $guardian->emergency_contact) }}" 
                           class="form-input @error('emergency_contact') border-red-500 @enderror" 
                           placeholder="+60123456789">
                    @error('emergency_contact')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Address -->
                <div class="md:col-span-2">
                    <label for="address" class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                    <textarea name="address" id="address" rows="3" 
                              class="form-textarea @error('address') border-red-500 @enderror"
                              placeholder="Enter complete address">{{ old('address', $guardian->user->address) }}</textarea>
                    @error('address')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Professional Information -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Professional Information</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Occupation -->
                <div>
                    <label for="occupation" class="block text-sm font-medium text-gray-700 mb-2">Occupation</label>
                    <input type="text" name="occupation" id="occupation" value="{{ old('occupation', $guardian->occupation) }}" 
                           class="form-input @error('occupation') border-red-500 @enderror" 
                           placeholder="e.g., Engineer, Teacher, Doctor">
                    @error('occupation')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Workplace -->
                <div>
                    <label for="workplace" class="block text-sm font-medium text-gray-700 mb-2">Workplace</label>
                    <input type="text" name="workplace" id="workplace" value="{{ old('workplace', $guardian->workplace) }}" 
                           class="form-input @error('workplace') border-red-500 @enderror" 
                           placeholder="e.g., ABC Company Sdn Bhd">
                    @error('workplace')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Monthly Income -->
                <div>
                    <label for="monthly_income" class="block text-sm font-medium text-gray-700 mb-2">Monthly Income (RM)</label>
                    <input type="number" name="monthly_income" id="monthly_income" value="{{ old('monthly_income', $guardian->monthly_income) }}" 
                           class="form-input @error('monthly_income') border-red-500 @enderror" 
                           placeholder="5000.00" step="0.01" min="0">
                    @error('monthly_income')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Children Assignment -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Children Assignment</h3>

            @if($students->count() > 0)
                <div class="space-y-3">
                    <label for="student-search" class="block text-sm font-medium text-gray-700">Select students that this guardian is responsible for:</label>

                    <!-- Multiselect Search Component -->
                    <div x-data="multiselectSearch()" class="relative"
                         x-init="
                            items = [
                                @foreach($students as $student)
                                {
                                    id: {{ $student->id }},
                                    name: '{{ addslashes($student->user->name ?? '') }}',
                                    subtitle: '{{ addslashes(($student->student_id ?? '') . ' • ' . ($student->class_section ?? '')) }}',
                                    searchText: '{{ addslashes(($student->user->name ?? '') . ' ' . ($student->student_id ?? '') . ' ' . ($student->class_section ?? '')) }}'
                                },
                                @endforeach
                            ];
                            selected = [{{ implode(',', old('student_ids', $guardian->students->pluck('id')->toArray())) }}];
                            name = 'student_ids[]';
                            placeholder = 'Search and select students...';
                            init();
                         ">

                        <!-- Search Input -->
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                            <input
                                type="text"
                                x-model="searchQuery"
                                @focus="showDropdown = true"
                                @click.away="showDropdown = false"
                                class="search-input"
                                :placeholder="placeholder"
                                autocomplete="off"
                            >
                        </div>

                        <!-- Selected Items -->
                        <div x-show="selectedItems.length > 0" class="mt-2 flex flex-wrap gap-2">
                            <template x-for="item in selectedItems" :key="item.id">
                                <span class="inline-flex items-center px-3 py-1 rounded-md text-sm bg-blue-100 text-blue-800">
                                    <span x-text="item.name"></span>
                                    <button type="button" @click="removeItem(item.id)" class="ml-2 text-blue-600 hover:text-blue-800">
                                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    </button>
                                </span>
                            </template>
                        </div>

                        <!-- Dropdown -->
                        <div x-show="showDropdown && filteredItems.length > 0"
                             x-transition:enter="transition ease-out duration-100"
                             x-transition:enter-start="transform opacity-0 scale-95"
                             x-transition:enter-end="transform opacity-100 scale-100"
                             x-transition:leave="transition ease-in duration-75"
                             x-transition:leave-start="transform opacity-100 scale-100"
                             x-transition:leave-end="transform opacity-0 scale-95"
                             class="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base border border-gray-200 overflow-auto focus:outline-none">
                            <template x-for="item in filteredItems" :key="item.id">
                                <div @click="toggleItem(item)"
                                     class="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-gray-50"
                                     :class="{ 'bg-blue-50': isSelected(item.id) }">
                                    <div class="flex items-center">
                                        <span class="font-medium block truncate" x-text="item.name"></span>
                                        <span x-show="isSelected(item.id)" class="absolute inset-y-0 right-0 flex items-center pr-4 text-blue-600">
                                            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                        </span>
                                    </div>
                                    <span class="text-gray-500 block truncate text-sm" x-text="item.subtitle"></span>
                                </div>
                            </template>
                        </div>

                        <!-- Hidden inputs for form submission -->
                        <template x-for="item in selectedItems" :key="item.id">
                            <input type="hidden" :name="name" :value="item.id">
                        </template>
                    </div>
                </div>
            @else
                <div class="text-center py-4">
                    <p class="text-sm text-gray-500">No students available. You can assign children later.</p>
                </div>
            @endif

            @error('student_ids')
                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
            @enderror
        </div>

        <!-- Account Information -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Account Information</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Account Status -->
                <div class="md:col-span-2">
                    <label class="flex items-center">
                        <input type="checkbox" name="is_active" value="1" 
                               class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                               {{ old('is_active', $guardian->user->is_active) ? 'checked' : '' }}>
                        <span class="ml-2 text-sm text-gray-700">Account is active</span>
                    </label>
                    @error('is_active')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Password -->
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">New Password</label>
                    <input type="password" name="password" id="password" 
                           class="form-input @error('password') border-red-500 @enderror" 
                           placeholder="Leave blank to keep current password">
                    @error('password')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Confirm Password -->
                <div>
                    <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
                    <input type="password" name="password_confirmation" id="password_confirmation" 
                           class="form-input @error('password_confirmation') border-red-500 @enderror" 
                           placeholder="Confirm new password">
                    @error('password_confirmation')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="flex justify-end space-x-4">
            <a href="{{ route('admin.guardians.show', $guardian) }}" class="btn-cancel">Cancel</a>
            <button type="submit" class="btn-primary">Update Guardian</button>
        </div>
    </form>
</div>

@push('scripts')
<script>
function multiselectSearch() {
    return {
        items: [],
        selectedItems: [],
        searchQuery: '',
        showDropdown: false,
        name: 'items[]',
        placeholder: 'Search and select items...',
        selected: [],

        init() {
            // Initialize selected items based on this.selected
            if (this.selected && this.selected.length > 0) {
                this.selectedItems = this.items.filter(item =>
                    this.selected.includes(parseInt(item.id))
                );
            }
        },

        get filteredItems() {
            if (!this.searchQuery) {
                return this.items.filter(item => !this.isSelected(item.id));
            }

            return this.items.filter(item => {
                const matchesSearch = item.searchText.toLowerCase().includes(this.searchQuery.toLowerCase());
                const notSelected = !this.isSelected(item.id);
                return matchesSearch && notSelected;
            });
        },

        isSelected(itemId) {
            return this.selectedItems.some(item => item.id == itemId);
        },

        toggleItem(item) {
            if (this.isSelected(item.id)) {
                this.removeItem(item.id);
            } else {
                this.selectedItems.push(item);
                this.searchQuery = '';
                this.showDropdown = false;
            }
        },

        removeItem(itemId) {
            this.selectedItems = this.selectedItems.filter(item => item.id != itemId);
        }
    };
}
</script>
@endpush
@endsection
