<!-- Toast Notification Container -->
<div id="toast-container" class="fixed bottom-4 right-4 z-[9999] space-y-2">
    <!-- Toast notifications will be dynamically added here -->
</div>

<script>
// Toast notification system with undo functionality
window.toastUndoActions = new Map();

window.showToast = function(message, type = 'info', duration = 5000, undoAction = null, undoData = null) {
    console.log('showToast called with:', { message, type, duration, undoAction: !!undoAction, undoData });

    const container = document.getElementById('toast-container');
    if (!container) {
        console.error('Toast container not found!');
        return;
    }

    const toast = document.createElement('div');

    // Create unique ID for this toast
    const toastId = 'toast-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    toast.id = toastId;

    console.log('Creating toast with ID:', toastId);
    
    // Set toast classes based on type
    let typeClasses = '';
    let icon = '';
    
    switch(type) {
        case 'success':
            typeClasses = 'bg-green-500 text-white';
            icon = `<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>`;
            break;
        case 'error':
            typeClasses = 'bg-red-500 text-white';
            icon = `<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
            </svg>`;
            break;
        case 'warning':
            typeClasses = 'bg-yellow-500 text-white';
            icon = `<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>`;
            break;
        default:
            typeClasses = 'bg-blue-500 text-white';
            icon = `<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>`;
    }
    
    toast.className = `toast ${typeClasses} transform translate-x-full opacity-0 transition-all duration-300 ease-in-out p-4 rounded-lg shadow-2xl max-w-sm min-w-80 border border-white/20`;

    // Store undo action if provided
    if (undoAction) {
        window.toastUndoActions.set(toastId, { action: undoAction, data: undoData });
    }

    const undoButton = undoAction ? `
        <div class="mt-3 flex items-center space-x-3">
            <button onclick="handleUndo('${toastId}')" class="inline-flex items-center px-3 py-1.5 text-xs font-medium bg-white bg-opacity-20 hover:bg-opacity-30 text-white rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50">
                <svg class="w-3 h-3 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                </svg>
                Undo
            </button>
            <button onclick="removeToast('${toastId}')" class="text-xs text-white text-opacity-70 hover:text-opacity-100 transition-colors duration-200 underline">
                Dismiss
            </button>
        </div>
    ` : '';

    toast.innerHTML = `
        <div class="flex items-start">
            <div class="flex-shrink-0 mt-0.5">
                ${icon}
            </div>
            <div class="ml-3 flex-1 min-w-0">
                <p class="text-sm font-medium leading-5">${message}</p>
                ${undoButton}
            </div>
            <div class="ml-4 flex-shrink-0">
                <button onclick="removeToast('${toastId}')" class="text-white hover:text-gray-200 focus:outline-none transition-colors duration-200 p-1 rounded">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
        </div>
    `;
    
    container.appendChild(toast);
    console.log('Toast added to container. Container children count:', container.children.length);

    // Trigger animation
    setTimeout(() => {
        toast.classList.remove('translate-x-full', 'opacity-0');
        toast.classList.add('translate-x-0', 'opacity-100');
        console.log('Toast animation triggered for:', toastId);
    }, 100);
    
    // Auto remove after duration
    if (duration > 0) {
        setTimeout(() => {
            removeToast(toastId);
        }, duration);
    }
};

window.removeToast = function(toastId) {
    const toast = document.getElementById(toastId);
    if (toast) {
        toast.classList.add('translate-x-full', 'opacity-0');
        setTimeout(() => {
            toast.remove();
            // Clean up undo action
            window.toastUndoActions.delete(toastId);
        }, 300);
    }
};

window.handleUndo = function(toastId) {
    const undoData = window.toastUndoActions.get(toastId);
    if (undoData && undoData.action) {
        undoData.action(undoData.data);
        removeToast(toastId);
    }
};

// Laravel session flash messages
<?php if(session('success')): ?>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Session data:', {
            success: '<?php echo e(session('success')); ?>',
            undo_action: '<?php echo e(session('undo_action')); ?>',
            undo_route: '<?php echo e(session('undo_route')); ?>',
            undo_data: <?php echo json_encode(session('undo_data'), 15, 512) ?>
        });

        <?php if(session('undo_action') && session('undo_data') && session('undo_route')): ?>
            console.log('Creating undo action for toast');
            const undoAction = function(data) {
                console.log('Undo action called with data:', data);
                fetch('<?php echo e(session('undo_route')); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        action: '<?php echo e(session('undo_action')); ?>',
                        data: data
                    })
                })
                .then(response => response.json())
                .then(result => {
                    console.log('Undo response:', result);
                    if (result.success) {
                        showToast(result.message, 'info');
                        // Optionally reload the page to reflect changes
                        setTimeout(() => window.location.reload(), 1000);
                    } else {
                        showToast(result.message || 'Failed to undo action', 'error');
                    }
                })
                .catch(error => {
                    console.error('Undo error:', error);
                    showToast('Failed to undo action', 'error');
                });
            };

            console.log('Calling showToast with undo action');
            showToast('<?php echo e(session('success')); ?>', 'success', 8000, undoAction, <?php echo json_encode(session('undo_data'), 15, 512) ?>);
        <?php else: ?>
            console.log('Calling showToast without undo action');
            showToast('<?php echo e(session('success')); ?>', 'success');
        <?php endif; ?>
    });
<?php endif; ?>

<?php if(session('error')): ?>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Error session found:', '<?php echo e(session('error')); ?>');
        console.log('Calling showToast for error message');
        showToast('<?php echo e(session('error')); ?>', 'error');
    });
<?php endif; ?>

<?php if(session('warning')): ?>
    document.addEventListener('DOMContentLoaded', function() {
        showToast('<?php echo e(session('warning')); ?>', 'warning');
    });
<?php endif; ?>

<?php if(session('info')): ?>
    document.addEventListener('DOMContentLoaded', function() {
        showToast('<?php echo e(session('info')); ?>', 'info');
    });
<?php endif; ?>
</script>
<?php /**PATH C:\xampp\htdocs\school-management-system\resources\views/components/toast.blade.php ENDPATH**/ ?>